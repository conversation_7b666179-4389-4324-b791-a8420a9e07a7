# Code generated by tool. DO NOT EDIT.
# This file is used to track the info used to scaffold your project
# and allow the plugins properly work.
# More info: https://book.kubebuilder.io/reference/project-config.html
domain: nginxpm-operator.io
layout:
- go.kubebuilder.io/v4
projectName: nginxpm-operator
repo: github.com/paradoxe35/nginxpm-operator
resources:
- api:
    crdVersion: v1
    namespaced: true
  controller: true
  domain: nginxpm-operator.io
  kind: Token
  path: github.com/paradoxe35/nginxpm-operator/api/v1
  version: v1
- api:
    crdVersion: v1
    namespaced: true
  controller: true
  domain: nginxpm-operator.io
  kind: ProxyHost
  path: github.com/paradoxe35/nginxpm-operator/api/v1
  version: v1
- api:
    crdVersion: v1
    namespaced: true
  controller: true
  domain: nginxpm-operator.io
  kind: LetsEncryptCertificate
  path: github.com/paradoxe35/nginxpm-operator/api/v1
  version: v1
- api:
    crdVersion: v1
    namespaced: true
  controller: true
  domain: nginxpm-operator.io
  kind: CustomCertificate
  path: github.com/paradoxe35/nginxpm-operator/api/v1
  version: v1
- api:
    crdVersion: v1
    namespaced: true
  controller: true
  domain: nginxpm-operator.io
  kind: AccessList
  path: github.com/paradoxe35/nginxpm-operator/api/v1
  version: v1
- api:
    crdVersion: v1
    namespaced: true
  controller: true
  domain: nginxpm-operator.io
  kind: Stream
  path: github.com/paradoxe35/nginxpm-operator/api/v1
  version: v1
version: "3"
