/*
Copyright 2024.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package base

import (
	"context"
	"errors"
	"fmt"

	"k8s.io/apimachinery/pkg/api/meta"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/tools/record"
	"k8s.io/client-go/util/retry"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
	"sigs.k8s.io/controller-runtime/pkg/log"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"

	nginxpmoperatoriov1 "github.com/paradoxe35/nginxpm-operator/api/v1"
	"github.com/paradoxe35/nginxpm-operator/internal/config"
	"github.com/paradoxe35/nginxpm-operator/pkg/nginxpm"
	"github.com/paradoxe35/nginxpm-operator/pkg/util"
)

// ReconcilableResource defines the interface for resources that can be reconciled
type ReconcilableResource interface {
	client.Object
	GetConditions() []metav1.Condition
	SetConditions([]metav1.Condition)
	GetTokenName() *nginxpmoperatoriov1.TokenName
}

// ResourceReconciler defines the interface for resource-specific reconciliation logic
type ResourceReconciler[T ReconcilableResource] interface {
	// GetFinalizer returns the finalizer name for this resource type
	GetFinalizer() string

	// CreateOrUpdate handles the creation or update of the resource
	CreateOrUpdate(ctx context.Context, req ctrl.Request, resource T, client *nginxpm.Client) error

	// Delete handles the deletion of the resource
	Delete(ctx context.Context, req ctrl.Request, resource T, client *nginxpm.Client) error

	// Validate performs resource-specific validation
	Validate(ctx context.Context, resource T) error
}

// BaseReconciler provides common reconciliation functionality
type BaseReconciler[T ReconcilableResource] struct {
	client.Client
	Scheme     *runtime.Scheme
	Recorder   record.EventRecorder
	Config     *config.ReconcilerConfig
	reconciler ResourceReconciler[T]
}

// NewBaseReconciler creates a new base reconciler
func NewBaseReconciler[T ReconcilableResource](
	client client.Client,
	scheme *runtime.Scheme,
	recorder record.EventRecorder,
	reconciler ResourceReconciler[T],
) *BaseReconciler[T] {
	return &BaseReconciler[T]{
		Client:     client,
		Scheme:     scheme,
		Recorder:   recorder,
		Config:     config.DefaultReconcilerConfig(),
		reconciler: reconciler,
	}
}

// Reconcile implements the main reconciliation loop
func (r *BaseReconciler[T]) Reconcile(ctx context.Context, req ctrl.Request, resource T) (ctrl.Result, error) {
	log := log.FromContext(ctx)

	// Fetch the resource instance
	err := r.Get(ctx, req.NamespacedName, resource)
	if err != nil {
		if client.IgnoreNotFound(err) == nil {
			log.Info("Resource not found. Ignoring since object must be deleted")
			return ctrl.Result{}, nil
		}
		log.Error(err, "Failed to get resource")
		return ctrl.Result{RequeueAfter: r.Config.RequeueAfter}, err
	}

	// Check if the resource is marked for deletion
	isMarkedToBeDeleted := !resource.GetDeletionTimestamp().IsZero()
	finalizer := r.reconciler.GetFinalizer()

	// Handle finalizer logic
	if !isMarkedToBeDeleted {
		if err := r.addFinalizer(ctx, finalizer, resource); err != nil {
			return ctrl.Result{RequeueAfter: r.Config.RequeueAfter}, err
		}
	}

	// Set initial status if no conditions exist
	if len(resource.GetConditions()) == 0 {
		r.updateStatus(ctx, req.NamespacedName, resource, func() {
			conditions := resource.GetConditions()
			meta.SetStatusCondition(&conditions, metav1.Condition{
				Status:             metav1.ConditionUnknown,
				Type:               config.ConditionTypeReconciling,
				Reason:             "Reconciling",
				Message:            "Starting reconciliation",
				LastTransitionTime: metav1.Now(),
			})
			resource.SetConditions(conditions)
		})
	}

	// Initialize NGINX PM client
	nginxpmClient, err := r.initNginxPMClient(ctx, req, resource.GetTokenName())
	if err != nil {
		if isMarkedToBeDeleted {
			// If we can't create client during deletion, just remove finalizer
			if err := r.removeFinalizer(ctx, finalizer, resource); err != nil {
				return ctrl.Result{RequeueAfter: r.Config.RequeueAfter}, err
			}
			return ctrl.Result{}, nil
		}

		r.recordEvent(resource, "Warning", config.EventReasonInitNginxPMClient,
			fmt.Sprintf("Failed to init nginxpm client: %s", err.Error()))

		r.updateStatus(ctx, req.NamespacedName, resource, func() {
			conditions := resource.GetConditions()
			meta.SetStatusCondition(&conditions, metav1.Condition{
				Status:             metav1.ConditionFalse,
				Type:               config.ConditionTypeError,
				Reason:             config.EventReasonInitNginxPMClient,
				Message:            err.Error(),
				LastTransitionTime: metav1.Now(),
			})
			resource.SetConditions(conditions)
		})

		return ctrl.Result{RequeueAfter: r.Config.RequeueAfter}, err
	}

	// Handle deletion
	if isMarkedToBeDeleted {
		if controllerutil.ContainsFinalizer(resource, finalizer) {
			log.Info("Performing finalizer operations")

			if err := r.reconciler.Delete(ctx, req, resource, nginxpmClient); err != nil {
				log.Error(err, "Failed to delete resource from remote NPM")
				r.recordEvent(resource, "Warning", config.EventReasonDelete,
					fmt.Sprintf("Failed to delete resource: %s", err.Error()))
				return ctrl.Result{RequeueAfter: r.Config.RequeueAfter}, err
			}

			if err := r.removeFinalizer(ctx, finalizer, resource); err != nil {
				return ctrl.Result{RequeueAfter: r.Config.RequeueAfter}, err
			}
		}
		return ctrl.Result{}, nil
	}

	// Validate the resource
	if err := r.reconciler.Validate(ctx, resource); err != nil {
		r.updateStatus(ctx, req.NamespacedName, resource, func() {
			conditions := resource.GetConditions()
			meta.SetStatusCondition(&conditions, metav1.Condition{
				Status:             metav1.ConditionFalse,
				Type:               config.ConditionTypeError,
				Reason:             config.EventReasonValidation,
				Message:            err.Error(),
				LastTransitionTime: metav1.Now(),
			})
			resource.SetConditions(conditions)
		})
		return ctrl.Result{RequeueAfter: r.Config.RequeueAfter}, err
	}

	// Create or update the resource
	if err := r.reconciler.CreateOrUpdate(ctx, req, resource, nginxpmClient); err != nil {
		r.updateStatus(ctx, req.NamespacedName, resource, func() {
			conditions := resource.GetConditions()
			meta.SetStatusCondition(&conditions, metav1.Condition{
				Status:             metav1.ConditionFalse,
				Type:               config.ConditionTypeError,
				Reason:             config.EventReasonCreateOrUpdate,
				Message:            err.Error(),
				LastTransitionTime: metav1.Now(),
			})
			resource.SetConditions(conditions)
		})
		return ctrl.Result{RequeueAfter: r.Config.RequeueAfter}, err
	}

	// Set success status
	r.updateStatus(ctx, req.NamespacedName, resource, func() {
		conditions := resource.GetConditions()
		meta.SetStatusCondition(&conditions, metav1.Condition{
			Status:             metav1.ConditionTrue,
			Type:               config.ConditionTypeReady,
			Reason:             config.EventReasonReconciliationSuccess,
			Message:            fmt.Sprintf("Resource reconciled successfully: %s", req.Name),
			LastTransitionTime: metav1.Now(),
		})
		resource.SetConditions(conditions)
	})

	return ctrl.Result{}, nil
}

// Helper methods
func (r *BaseReconciler[T]) addFinalizer(ctx context.Context, finalizer string, obj client.Object) error {
	if !controllerutil.ContainsFinalizer(obj, finalizer) {
		controllerutil.AddFinalizer(obj, finalizer)
		return r.Update(ctx, obj)
	}
	return nil
}

func (r *BaseReconciler[T]) removeFinalizer(ctx context.Context, finalizer string, obj client.Object) error {
	if controllerutil.ContainsFinalizer(obj, finalizer) {
		controllerutil.RemoveFinalizer(obj, finalizer)
		return r.Update(ctx, obj)
	}
	return nil
}

func (r *BaseReconciler[T]) recordEvent(obj client.Object, eventType, reason, message string) {
	r.Recorder.Event(obj, eventType, reason, message)
}

func (r *BaseReconciler[T]) updateStatus(ctx context.Context, namespacedName types.NamespacedName, obj client.Object, mutate func()) error {
	return UpdateStatus(ctx, r.Client, obj, namespacedName, mutate)
}

func (r *BaseReconciler[T]) initNginxPMClient(ctx context.Context, req ctrl.Request, tokenName *nginxpmoperatoriov1.TokenName) (*nginxpm.Client, error) {
	return InitNginxPMClient(ctx, r, req, tokenName)
}

// InitNginxPMClient initializes a new NGINX Proxy Manager client
func InitNginxPMClient(ctx context.Context, r client.Reader, req reconcile.Request, tokenName *nginxpmoperatoriov1.TokenName) (*nginxpm.Client, error) {
	log := log.FromContext(ctx)

	// Set the token names
	names := []string{config.TokenDefaultName}
	if tokenName != nil && len(tokenName.Name) > 0 {
		// Prepend token name
		names = append([]string{tokenName.Name}, names...)
	}

	// Set the token namespaces
	namespaces := []string{req.Namespace, config.TokenSystemNamespace, config.TokenDefaultNamespace}
	if tokenName != nil && tokenName.Namespace != nil && len(*tokenName.Namespace) > 0 {
		// Prepend token namespace
		namespaces = append([]string{*tokenName.Namespace}, namespaces...)
	}

	token := &nginxpmoperatoriov1.Token{}

	for _, namespace := range namespaces {
		found := false
		for _, name := range names {
			tokenNamespaced := types.NamespacedName{
				Namespace: namespace,
				Name:      name,
			}

			// Get the token resource
			err := r.Get(ctx, tokenNamespaced, token)
			if err == nil {
				log.Info("Token resource found", "Namespace", namespace, "Name", name)
				found = true
				break
			}
		}

		// token found on this iteration
		if found {
			break
		}
	}

	// If token still empty, means it was not found
	if token.Name == "" || token.Status.Token == nil {
		err := errors.New("token resource not found")
		log.Error(err, "Token resource not found")
		return nil, err
	}

	// Create a new Nginx Proxy Manager client
	nginxpmClient := nginxpm.NewClientFromToken(util.NewHttpClient(), token)

	// Check if the connection is established
	if err := nginxpmClient.CheckTokenAccess(); err != nil {
		log.Error(err, "Token access check failed")
		return nil, err
	}

	log.Info("NginxPM client initialized successfully")

	return nginxpmClient, nil
}

// UpdateStatus updates the status of a resource with retry logic
func UpdateStatus(ctx context.Context, r client.Client, object client.Object, namespacedName types.NamespacedName, mutate func()) error {
	log := log.FromContext(ctx)

	err := retry.RetryOnConflict(retry.DefaultRetry, func() error {
		err := r.Get(ctx, namespacedName, object)
		if err != nil {
			return err
		}

		mutate()

		// Update the object status
		return r.Status().Update(ctx, object)
	})

	if err != nil {
		log.Error(err, "Failed to update resource status")
		return err
	}

	return nil
}
