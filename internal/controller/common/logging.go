/*
Copyright 2024.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package common

import (
	"context"
	"fmt"

	"github.com/go-logr/logr"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"
)

// LogContext provides structured logging with context
type LogContext struct {
	logger    logr.Logger
	component string
	resource  string
	namespace string
	name      string
}

// NewLogContext creates a new logging context
func NewLogContext(ctx context.Context, component string) *LogContext {
	return &LogContext{
		logger:    log.FromContext(ctx),
		component: component,
	}
}

// NewLogContextForResource creates a new logging context for a specific resource
func NewLogContextForResource(ctx context.Context, component string, obj client.Object) *LogContext {
	return &LogContext{
		logger:    log.FromContext(ctx),
		component: component,
		resource:  fmt.Sprintf("%T", obj),
		namespace: obj.GetNamespace(),
		name:      obj.GetName(),
	}
}

// NewLogContextForRequest creates a new logging context for a reconcile request
func NewLogContextForRequest(ctx context.Context, component string, req reconcile.Request) *LogContext {
	return &LogContext{
		logger:    log.FromContext(ctx),
		component: component,
		namespace: req.Namespace,
		name:      req.Name,
	}
}

// WithValues adds key-value pairs to the logger
func (lc *LogContext) WithValues(keysAndValues ...interface{}) *LogContext {
	return &LogContext{
		logger:    lc.logger.WithValues(keysAndValues...),
		component: lc.component,
		resource:  lc.resource,
		namespace: lc.namespace,
		name:      lc.name,
	}
}

// WithName adds a name to the logger
func (lc *LogContext) WithName(name string) *LogContext {
	return &LogContext{
		logger:    lc.logger.WithName(name),
		component: lc.component,
		resource:  lc.resource,
		namespace: lc.namespace,
		name:      lc.name,
	}
}

// getBaseValues returns the base key-value pairs for logging
func (lc *LogContext) getBaseValues() []interface{} {
	values := []interface{}{}
	
	if lc.component != "" {
		values = append(values, "component", lc.component)
	}
	
	if lc.resource != "" {
		values = append(values, "resource", lc.resource)
	}
	
	if lc.namespace != "" {
		values = append(values, "namespace", lc.namespace)
	}
	
	if lc.name != "" {
		values = append(values, "name", lc.name)
	}
	
	return values
}

// Info logs an info message with context
func (lc *LogContext) Info(msg string, keysAndValues ...interface{}) {
	values := append(lc.getBaseValues(), keysAndValues...)
	lc.logger.Info(msg, values...)
}

// Error logs an error message with context
func (lc *LogContext) Error(err error, msg string, keysAndValues ...interface{}) {
	values := append(lc.getBaseValues(), keysAndValues...)
	lc.logger.Error(err, msg, values...)
}

// Debug logs a debug message with context (V(1))
func (lc *LogContext) Debug(msg string, keysAndValues ...interface{}) {
	values := append(lc.getBaseValues(), keysAndValues...)
	lc.logger.V(1).Info(msg, values...)
}

// Trace logs a trace message with context (V(2))
func (lc *LogContext) Trace(msg string, keysAndValues ...interface{}) {
	values := append(lc.getBaseValues(), keysAndValues...)
	lc.logger.V(2).Info(msg, values...)
}

// ReconciliationStarted logs the start of reconciliation
func (lc *LogContext) ReconciliationStarted() {
	lc.Info("Starting reconciliation")
}

// ReconciliationCompleted logs successful completion of reconciliation
func (lc *LogContext) ReconciliationCompleted(duration string) {
	lc.Info("Reconciliation completed successfully", "duration", duration)
}

// ReconciliationFailed logs failed reconciliation
func (lc *LogContext) ReconciliationFailed(err error, duration string) {
	lc.Error(err, "Reconciliation failed", "duration", duration)
}

// ResourceCreated logs resource creation
func (lc *LogContext) ResourceCreated(resourceType string, id interface{}) {
	lc.Info("Resource created successfully", "resourceType", resourceType, "id", id)
}

// ResourceUpdated logs resource update
func (lc *LogContext) ResourceUpdated(resourceType string, id interface{}) {
	lc.Info("Resource updated successfully", "resourceType", resourceType, "id", id)
}

// ResourceDeleted logs resource deletion
func (lc *LogContext) ResourceDeleted(resourceType string, id interface{}) {
	lc.Info("Resource deleted successfully", "resourceType", resourceType, "id", id)
}

// ClientInitialized logs successful client initialization
func (lc *LogContext) ClientInitialized(endpoint string) {
	lc.Info("NGINX PM client initialized successfully", "endpoint", endpoint)
}

// ClientInitializationFailed logs failed client initialization
func (lc *LogContext) ClientInitializationFailed(err error, endpoint string) {
	lc.Error(err, "Failed to initialize NGINX PM client", "endpoint", endpoint)
}

// TokenFound logs successful token discovery
func (lc *LogContext) TokenFound(tokenNamespace, tokenName string) {
	lc.Info("Token resource found", "tokenNamespace", tokenNamespace, "tokenName", tokenName)
}

// TokenNotFound logs failed token discovery
func (lc *LogContext) TokenNotFound(searchedNamespaces, searchedNames []string) {
	lc.Error(nil, "Token resource not found", 
		"searchedNamespaces", searchedNamespaces, 
		"searchedNames", searchedNames)
}

// ValidationFailed logs validation failures
func (lc *LogContext) ValidationFailed(err error, validationType string) {
	lc.Error(err, "Validation failed", "validationType", validationType)
}

// FinalizerAdded logs finalizer addition
func (lc *LogContext) FinalizerAdded(finalizer string) {
	lc.Debug("Finalizer added", "finalizer", finalizer)
}

// FinalizerRemoved logs finalizer removal
func (lc *LogContext) FinalizerRemoved(finalizer string) {
	lc.Debug("Finalizer removed", "finalizer", finalizer)
}

// StatusUpdated logs status updates
func (lc *LogContext) StatusUpdated(conditionType string, status string) {
	lc.Debug("Status updated", "conditionType", conditionType, "status", status)
}

// EventRecorded logs event recording
func (lc *LogContext) EventRecorded(eventType, reason, message string) {
	lc.Debug("Event recorded", "eventType", eventType, "reason", reason, "message", message)
}

// OperationRetried logs operation retries
func (lc *LogContext) OperationRetried(operation string, attempt int, err error) {
	lc.Info("Retrying operation", "operation", operation, "attempt", attempt, "error", err.Error())
}

// ConfigurationLoaded logs configuration loading
func (lc *LogContext) ConfigurationLoaded(configType string) {
	lc.Debug("Configuration loaded", "configType", configType)
}

// HealthCheckPassed logs successful health checks
func (lc *LogContext) HealthCheckPassed(checkType string) {
	lc.Debug("Health check passed", "checkType", checkType)
}

// HealthCheckFailed logs failed health checks
func (lc *LogContext) HealthCheckFailed(err error, checkType string) {
	lc.Error(err, "Health check failed", "checkType", checkType)
}
