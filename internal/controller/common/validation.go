/*
Copyright 2024.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package common

import (
	"context"
	"fmt"
	"net"
	"net/url"
	"regexp"
	"strings"

	"k8s.io/apimachinery/pkg/fields"
	"sigs.k8s.io/controller-runtime/pkg/client"

	nginxpmoperatoriov1 "github.com/paradoxe35/nginxpm-operator/api/v1"
)

// DomainValidator provides domain validation functionality
type DomainValidator struct {
	client client.Reader
}

// NewDomainValidator creates a new domain validator
func NewDomainValidator(client client.Reader) *DomainValidator {
	return &DomainValidator{client: client}
}

// ValidateDomainUniqueness checks if domains are unique across all ProxyHost resources
func (v *DomainValidator) ValidateDomainUniqueness(ctx context.Context, proxyHost *nginxpmoperatoriov1.ProxyHost) error {
	// Get all ProxyHost resources
	proxyHostList := &nginxpmoperatoriov1.ProxyHostList{}
	if err := v.client.List(ctx, proxyHostList); err != nil {
		return ValidationErrorWithCause("failed to list ProxyHost resources", err)
	}

	// Create a map of domains to their owning resources
	domainMap := make(map[string]string)

	for _, ph := range proxyHostList.Items {
		// Skip the current resource being validated
		if ph.Name == proxyHost.Name && ph.Namespace == proxyHost.Namespace {
			continue
		}

		for _, domain := range ph.Spec.DomainNames {
			domainStr := string(domain)
			if existing, exists := domainMap[domainStr]; exists {
				return ValidationError(fmt.Sprintf("domain %s is already used by %s", domainStr, existing))
			}
			domainMap[domainStr] = fmt.Sprintf("%s/%s", ph.Namespace, ph.Name)
		}
	}

	// Check if any of the current resource's domains conflict
	for _, domain := range proxyHost.Spec.DomainNames {
		domainStr := string(domain)
		if existing, exists := domainMap[domainStr]; exists {
			return ValidationError(fmt.Sprintf("domain %s is already used by %s", domainStr, existing))
		}
	}

	return nil
}

// ValidateDomainFormat validates the format of domain names
func ValidateDomainFormat(domains []nginxpmoperatoriov1.DomainName) error {
	domainRegex := regexp.MustCompile(`^(\*\.)?[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z]{2,}$`)

	for _, domain := range domains {
		domainStr := string(domain)
		if !domainRegex.MatchString(domainStr) {
			return ValidationError(fmt.Sprintf("invalid domain format: %s", domainStr))
		}

		// Additional validation for wildcard domains
		if strings.HasPrefix(domainStr, "*.") {
			// Ensure wildcard is only at the beginning
			if strings.Count(domainStr, "*") > 1 {
				return ValidationError(fmt.Sprintf("invalid wildcard domain: %s (multiple wildcards not allowed)", domainStr))
			}

			// Ensure there's a valid domain after the wildcard
			baseDomain := strings.TrimPrefix(domainStr, "*.")
			if len(baseDomain) == 0 || !domainRegex.MatchString(baseDomain) {
				return ValidationError(fmt.Sprintf("invalid wildcard domain: %s (invalid base domain)", domainStr))
			}
		}
	}

	return nil
}

// ValidateHostname validates hostname format (for forward hosts)
func ValidateHostname(hostname string) error {
	if hostname == "" {
		return ValidationError("hostname cannot be empty")
	}

	// Check if it's an IP address
	if net.ParseIP(hostname) != nil {
		return nil // Valid IP address
	}

	// Validate as hostname
	hostnameRegex := regexp.MustCompile(`^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$`)
	if !hostnameRegex.MatchString(hostname) {
		return ValidationError(fmt.Sprintf("invalid hostname format: %s", hostname))
	}

	return nil
}

// ValidatePort validates port numbers
func ValidatePort(port int32) error {
	if port < 1 || port > 65535 {
		return ValidationError(fmt.Sprintf("invalid port number: %d (must be between 1 and 65535)", port))
	}
	return nil
}

// ValidateURL validates URL format
func ValidateURL(urlStr string) error {
	if urlStr == "" {
		return ValidationError("URL cannot be empty")
	}

	parsedURL, err := url.Parse(urlStr)
	if err != nil {
		return ValidationErrorWithCause(fmt.Sprintf("invalid URL format: %s", urlStr), err)
	}

	if parsedURL.Scheme == "" {
		return ValidationError(fmt.Sprintf("URL must include scheme: %s", urlStr))
	}

	if parsedURL.Host == "" {
		return ValidationError(fmt.Sprintf("URL must include host: %s", urlStr))
	}

	return nil
}

// ServiceValidator provides service validation functionality
type ServiceValidator struct {
	client client.Reader
}

// NewServiceValidator creates a new service validator
func NewServiceValidator(client client.Reader) *ServiceValidator {
	return &ServiceValidator{client: client}
}

// ValidateServiceExists checks if a service exists and is accessible
func (v *ServiceValidator) ValidateServiceExists(ctx context.Context, namespace, name string) error {
	if name == "" {
		return ValidationError("service name cannot be empty")
	}

	serviceList := &nginxpmoperatoriov1.ProxyHostList{}
	listOpts := &client.ListOptions{
		FieldSelector: fields.OneTermEqualSelector(".spec.forward.service.name", name),
		Namespace:     namespace,
	}

	if err := v.client.List(ctx, serviceList, listOpts); err != nil {
		return ValidationErrorWithCause(fmt.Sprintf("failed to validate service %s/%s", namespace, name), err)
	}

	return nil
}

// CertificateValidator provides certificate validation functionality
type CertificateValidator struct {
	client client.Reader
}

// NewCertificateValidator creates a new certificate validator
func NewCertificateValidator(client client.Reader) *CertificateValidator {
	return &CertificateValidator{client: client}
}

// ValidateCertificateReference checks if a certificate reference is valid
func (v *CertificateValidator) ValidateCertificateReference(ctx context.Context, ssl *nginxpmoperatoriov1.ProxyHostSsl) error {
	if ssl == nil {
		return nil // SSL is optional
	}

	// Count how many certificate types are specified
	certCount := 0
	if ssl.CustomCertificate != nil {
		certCount++
	}
	if ssl.LetsEncryptCertificate != nil {
		certCount++
	}
	if ssl.CertificateId != nil {
		certCount++
	}

	if certCount > 1 {
		return ValidationError("only one certificate type can be specified (customCertificate, letsEncryptCertificate, or certificateId)")
	}

	if certCount == 0 {
		return ValidationError("at least one certificate type must be specified when SSL is enabled")
	}

	return nil
}

// TokenValidator provides token validation functionality
type TokenValidator struct {
	client client.Reader
}

// NewTokenValidator creates a new token validator
func NewTokenValidator(client client.Reader) *TokenValidator {
	return &TokenValidator{client: client}
}

// ValidateTokenReference checks if a token reference is valid
func (v *TokenValidator) ValidateTokenReference(ctx context.Context, namespace string, tokenName *nginxpmoperatoriov1.TokenName) error {
	if tokenName == nil {
		return nil // Token reference is optional (will use default)
	}

	if tokenName.Name == "" {
		return ValidationError("token name cannot be empty when token reference is specified")
	}

	// Validate token name format
	tokenNameRegex := regexp.MustCompile(`^[a-z0-9]([-a-z0-9]*[a-z0-9])?$`)
	if !tokenNameRegex.MatchString(tokenName.Name) {
		return ValidationError(fmt.Sprintf("invalid token name format: %s", tokenName.Name))
	}

	// Validate namespace if specified
	if tokenName.Namespace != nil {
		if *tokenName.Namespace == "" {
			return ValidationError("token namespace cannot be empty when specified")
		}

		namespaceRegex := regexp.MustCompile(`^[a-z0-9]([-a-z0-9]*[a-z0-9])?$`)
		if !namespaceRegex.MatchString(*tokenName.Namespace) {
			return ValidationError(fmt.Sprintf("invalid token namespace format: %s", *tokenName.Namespace))
		}
	}

	return nil
}

// AccessListValidator provides access list validation functionality
type AccessListValidator struct {
	client client.Reader
}

// NewAccessListValidator creates a new access list validator
func NewAccessListValidator(client client.Reader) *AccessListValidator {
	return &AccessListValidator{client: client}
}

// ValidateAccessListReference checks if an access list reference is valid
func (v *AccessListValidator) ValidateAccessListReference(ctx context.Context, accessList *nginxpmoperatoriov1.ProxyHostAccessList) error {
	if accessList == nil {
		return nil // Access list is optional
	}

	if accessList.Name == "" {
		return ValidationError("access list name cannot be empty when access list reference is specified")
	}

	// Validate access list name format
	nameRegex := regexp.MustCompile(`^[a-z0-9]([-a-z0-9]*[a-z0-9])?$`)
	if !nameRegex.MatchString(accessList.Name) {
		return ValidationError(fmt.Sprintf("invalid access list name format: %s", accessList.Name))
	}

	// Validate namespace if specified
	if accessList.Namespace != nil {
		if *accessList.Namespace == "" {
			return ValidationError("access list namespace cannot be empty when specified")
		}

		namespaceRegex := regexp.MustCompile(`^[a-z0-9]([-a-z0-9]*[a-z0-9])?$`)
		if !namespaceRegex.MatchString(*accessList.Namespace) {
			return ValidationError(fmt.Sprintf("invalid access list namespace format: %s", *accessList.Namespace))
		}
	}

	return nil
}
