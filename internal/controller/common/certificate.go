/*
Copyright 2024.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package common

import (
	"context"
	"fmt"

	"k8s.io/apimachinery/pkg/types"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"

	nginxpmoperatoriov1 "github.com/paradoxe35/nginxpm-operator/api/v1"
	"github.com/paradoxe35/nginxpm-operator/pkg/nginxpm"
)

// CertificateManager handles certificate operations
type CertificateManager struct {
	client client.Reader
	logger *LogContext
}

// NewCertificateManager creates a new certificate manager
func NewCertificateManager(client client.Reader, logger *LogContext) *CertificateManager {
	return &CertificateManager{
		client: client,
		logger: logger,
	}
}

// CertificateRetrievalOptions contains options for certificate retrieval
type CertificateRetrievalOptions struct {
	Ctx                    context.Context
	Req                    reconcile.Request
	NginxpmClient          *nginxpm.Client
	LetsEncryptCertificate *nginxpmoperatoriov1.SslLetsEncryptCertificate
	CustomCertificate      *nginxpmoperatoriov1.SslCustomCertificate
	CertificateId          *int
}

// RetrieveCertificate retrieves a certificate based on the provided options
func (cm *CertificateManager) RetrieveCertificate(opts CertificateRetrievalOptions) (*nginxpm.Certificate, error) {
	// Validate that only one certificate type is specified
	certCount := 0
	if opts.LetsEncryptCertificate != nil {
		certCount++
	}
	if opts.CustomCertificate != nil {
		certCount++
	}
	if opts.CertificateId != nil {
		certCount++
	}

	if certCount > 1 {
		return nil, ValidationError("only one certificate type can be specified")
	}

	if certCount == 0 {
		return nil, nil // No certificate specified
	}

	// Handle Let's Encrypt certificate
	if opts.LetsEncryptCertificate != nil {
		return cm.retrieveLetsEncryptCertificate(opts.Ctx, opts.Req, opts.LetsEncryptCertificate, opts.NginxpmClient)
	}

	// Handle custom certificate
	if opts.CustomCertificate != nil {
		return cm.retrieveCustomCertificate(opts.Ctx, opts.Req, opts.CustomCertificate, opts.NginxpmClient)
	}

	// Handle certificate ID
	if opts.CertificateId != nil {
		return cm.retrieveCertificateById(*opts.CertificateId, opts.NginxpmClient)
	}

	return nil, nil
}

// retrieveLetsEncryptCertificate retrieves a Let's Encrypt certificate
func (cm *CertificateManager) retrieveLetsEncryptCertificate(
	ctx context.Context,
	req reconcile.Request,
	leCert *nginxpmoperatoriov1.SslLetsEncryptCertificate,
	nginxpmClient *nginxpm.Client,
) (*nginxpm.Certificate, error) {
	cm.logger.Debug("Retrieving Let's Encrypt certificate", "certificateName", leCert.Name)

	// Determine namespace
	namespace := req.Namespace
	if leCert.Namespace != nil && *leCert.Namespace != "" {
		namespace = *leCert.Namespace
	}

	// Get the Let's Encrypt certificate resource
	letsEncryptCert := &nginxpmoperatoriov1.LetsEncryptCertificate{}
	namespacedName := types.NamespacedName{
		Namespace: namespace,
		Name:      leCert.Name,
	}

	if err := cm.client.Get(ctx, namespacedName, letsEncryptCert); err != nil {
		return nil, NotFoundErrorWithCause(
			fmt.Sprintf("Let's Encrypt certificate %s/%s not found", namespace, leCert.Name),
			err,
		)
	}

	// Check if the certificate has an ID in its status
	if letsEncryptCert.Status.Id == nil {
		return nil, ValidationError(
			fmt.Sprintf("Let's Encrypt certificate %s/%s does not have an ID in status", namespace, leCert.Name),
		)
	}

	// Retrieve the certificate from NGINX PM
	return cm.retrieveCertificateById(*letsEncryptCert.Status.Id, nginxpmClient)
}

// retrieveCustomCertificate retrieves a custom certificate
func (cm *CertificateManager) retrieveCustomCertificate(
	ctx context.Context,
	req reconcile.Request,
	customCert *nginxpmoperatoriov1.SslCustomCertificate,
	nginxpmClient *nginxpm.Client,
) (*nginxpm.Certificate, error) {
	cm.logger.Debug("Retrieving custom certificate", "certificateName", customCert.Name)

	// Determine namespace
	namespace := req.Namespace
	if customCert.Namespace != nil && *customCert.Namespace != "" {
		namespace = *customCert.Namespace
	}

	// Get the custom certificate resource
	customCertificate := &nginxpmoperatoriov1.CustomCertificate{}
	namespacedName := types.NamespacedName{
		Namespace: namespace,
		Name:      customCert.Name,
	}

	if err := cm.client.Get(ctx, namespacedName, customCertificate); err != nil {
		return nil, NotFoundErrorWithCause(
			fmt.Sprintf("Custom certificate %s/%s not found", namespace, customCert.Name),
			err,
		)
	}

	// Check if the certificate has an ID in its status
	if customCertificate.Status.Id == nil {
		return nil, ValidationError(
			fmt.Sprintf("Custom certificate %s/%s does not have an ID in status", namespace, customCert.Name),
		)
	}

	// Retrieve the certificate from NGINX PM
	return cm.retrieveCertificateById(*customCertificate.Status.Id, nginxpmClient)
}

// retrieveCertificateById retrieves a certificate by its ID
func (cm *CertificateManager) retrieveCertificateById(
	certificateId int,
	nginxpmClient *nginxpm.Client,
) (*nginxpm.Certificate, error) {
	cm.logger.Debug("Retrieving certificate by ID", "certificateId", certificateId)

	certificate, err := nginxpmClient.FindCertificateByID(certificateId)
	if err != nil {
		return nil, ConnectionErrorWithCause(
			fmt.Sprintf("Failed to retrieve certificate with ID %d", certificateId),
			err,
		)
	}

	if certificate == nil {
		return nil, NotFoundError(
			fmt.Sprintf("Certificate with ID %d not found in NGINX PM", certificateId),
		)
	}

	cm.logger.Debug("Certificate retrieved successfully", "certificateId", certificateId)
	return certificate, nil
}

// ValidateCertificateConfiguration validates certificate configuration
func (cm *CertificateManager) ValidateCertificateConfiguration(ssl *nginxpmoperatoriov1.ProxyHostSsl) error {
	if ssl == nil {
		return nil // SSL is optional
	}

	// Count certificate types
	certCount := 0
	if ssl.LetsEncryptCertificate != nil {
		certCount++
	}
	if ssl.CustomCertificate != nil {
		certCount++
	}
	if ssl.CertificateId != nil {
		certCount++
	}

	if certCount > 1 {
		return ValidationError("only one certificate type can be specified (letsEncryptCertificate, customCertificate, or certificateId)")
	}

	if certCount == 0 {
		return ValidationError("at least one certificate type must be specified when SSL is enabled")
	}

	// Validate Let's Encrypt certificate reference
	if ssl.LetsEncryptCertificate != nil {
		if ssl.LetsEncryptCertificate.Name == "" {
			return ValidationError("Let's Encrypt certificate name cannot be empty")
		}
	}

	// Validate custom certificate reference
	if ssl.CustomCertificate != nil {
		if ssl.CustomCertificate.Name == "" {
			return ValidationError("custom certificate name cannot be empty")
		}
	}

	// Validate certificate ID
	if ssl.CertificateId != nil {
		if *ssl.CertificateId <= 0 {
			return ValidationError("certificate ID must be a positive integer")
		}
	}

	return nil
}

// GetCertificateInfo returns information about the certificate configuration
func (cm *CertificateManager) GetCertificateInfo(ssl *nginxpmoperatoriov1.ProxyHostSsl) string {
	if ssl == nil {
		return "No SSL configuration"
	}

	if ssl.LetsEncryptCertificate != nil {
		namespace := "default"
		if ssl.LetsEncryptCertificate.Namespace != nil {
			namespace = *ssl.LetsEncryptCertificate.Namespace
		}
		return fmt.Sprintf("Let's Encrypt certificate: %s/%s", namespace, ssl.LetsEncryptCertificate.Name)
	}

	if ssl.CustomCertificate != nil {
		namespace := "default"
		if ssl.CustomCertificate.Namespace != nil {
			namespace = *ssl.CustomCertificate.Namespace
		}
		return fmt.Sprintf("Custom certificate: %s/%s", namespace, ssl.CustomCertificate.Name)
	}

	if ssl.CertificateId != nil {
		return fmt.Sprintf("Certificate ID: %d", *ssl.CertificateId)
	}

	return "Invalid SSL configuration"
}
