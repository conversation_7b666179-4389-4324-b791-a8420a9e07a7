/*
Copyright 2024.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package common

import (
	"context"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/fields"
	"k8s.io/apimachinery/pkg/types"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/builder"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/handler"
	"sigs.k8s.io/controller-runtime/pkg/predicate"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"

	nginxpmoperatoriov1 "github.com/paradoxe35/nginxpm-operator/api/v1"
	"github.com/paradoxe35/nginxpm-operator/internal/config"
)

// ControllerSetupHelper provides utilities for setting up controllers
type ControllerSetupHelper struct {
	mgr    ctrl.Manager
	logger *LogContext
}

// NewControllerSetupHelper creates a new controller setup helper
func NewControllerSetupHelper(mgr ctrl.Manager, logger *LogContext) *ControllerSetupHelper {
	return &ControllerSetupHelper{
		mgr:    mgr,
		logger: logger,
	}
}

// SetupFieldIndexer sets up field indexers for efficient querying
func (h *ControllerSetupHelper) SetupFieldIndexer(obj client.Object, field string, extractFunc func(client.Object) []string) error {
	h.logger.Debug("Setting up field indexer", "field", field, "objectType", obj.GetObjectKind().GroupVersionKind().Kind)
	
	return h.mgr.GetFieldIndexer().IndexField(
		context.Background(),
		obj,
		field,
		extractFunc,
	)
}

// SetupTokenIndexer sets up the token field indexer
func (h *ControllerSetupHelper) SetupTokenIndexer(obj client.Object) error {
	return h.SetupFieldIndexer(obj, config.TokenField, func(rawObj client.Object) []string {
		switch o := rawObj.(type) {
		case *nginxpmoperatoriov1.ProxyHost:
			if o.Spec.Token == nil {
				return []string{config.TokenDefaultName}
			}
			if o.Spec.Token.Name == "" {
				return nil
			}
			return []string{o.Spec.Token.Name}
		case *nginxpmoperatoriov1.Stream:
			if o.Spec.Token == nil {
				return []string{config.TokenDefaultName}
			}
			if o.Spec.Token.Name == "" {
				return nil
			}
			return []string{o.Spec.Token.Name}
		default:
			return nil
		}
	})
}

// SetupCustomCertificateIndexer sets up the custom certificate field indexer
func (h *ControllerSetupHelper) SetupCustomCertificateIndexer(obj client.Object) error {
	return h.SetupFieldIndexer(obj, config.CustomCertificateField, func(rawObj client.Object) []string {
		switch o := rawObj.(type) {
		case *nginxpmoperatoriov1.ProxyHost:
			if o.Spec.Ssl == nil || o.Spec.Ssl.CustomCertificate == nil || o.Spec.Ssl.CustomCertificate.Name == "" {
				return nil
			}
			return []string{o.Spec.Ssl.CustomCertificate.Name}
		case *nginxpmoperatoriov1.Stream:
			if o.Spec.Ssl == nil || o.Spec.Ssl.CustomCertificate == nil || o.Spec.Ssl.CustomCertificate.Name == "" {
				return nil
			}
			return []string{o.Spec.Ssl.CustomCertificate.Name}
		default:
			return nil
		}
	})
}

// SetupLetsEncryptCertificateIndexer sets up the Let's Encrypt certificate field indexer
func (h *ControllerSetupHelper) SetupLetsEncryptCertificateIndexer(obj client.Object) error {
	return h.SetupFieldIndexer(obj, config.LetsEncryptCertificateField, func(rawObj client.Object) []string {
		switch o := rawObj.(type) {
		case *nginxpmoperatoriov1.ProxyHost:
			if o.Spec.Ssl == nil || o.Spec.Ssl.LetsEncryptCertificate == nil || o.Spec.Ssl.LetsEncryptCertificate.Name == "" {
				return nil
			}
			return []string{o.Spec.Ssl.LetsEncryptCertificate.Name}
		case *nginxpmoperatoriov1.Stream:
			if o.Spec.Ssl == nil || o.Spec.Ssl.LetsEncryptCertificate == nil || o.Spec.Ssl.LetsEncryptCertificate.Name == "" {
				return nil
			}
			return []string{o.Spec.Ssl.LetsEncryptCertificate.Name}
		default:
			return nil
		}
	})
}

// SetupAccessListIndexer sets up the access list field indexer
func (h *ControllerSetupHelper) SetupAccessListIndexer(obj client.Object) error {
	return h.SetupFieldIndexer(obj, config.AccessListField, func(rawObj client.Object) []string {
		switch o := rawObj.(type) {
		case *nginxpmoperatoriov1.ProxyHost:
			if o.Spec.AccessList == nil || o.Spec.AccessList.Name == "" {
				return nil
			}
			return []string{o.Spec.AccessList.Name}
		default:
			return nil
		}
	})
}

// SetupForwardServiceIndexer sets up the forward service field indexer
func (h *ControllerSetupHelper) SetupForwardServiceIndexer(obj client.Object) error {
	return h.SetupFieldIndexer(obj, config.ForwardServiceField, func(rawObj client.Object) []string {
		switch o := rawObj.(type) {
		case *nginxpmoperatoriov1.ProxyHost:
			if o.Spec.Forward.Service == nil || o.Spec.Forward.Service.Name == "" {
				return nil
			}
			return []string{o.Spec.Forward.Service.Name}
		case *nginxpmoperatoriov1.Stream:
			if o.Spec.Forward.Service == nil || o.Spec.Forward.Service.Name == "" {
				return nil
			}
			return []string{o.Spec.Forward.Service.Name}
		default:
			return nil
		}
	})
}

// SetupTokenSecretIndexer sets up the token secret field indexer
func (h *ControllerSetupHelper) SetupTokenSecretIndexer() error {
	return h.SetupFieldIndexer(&nginxpmoperatoriov1.Token{}, config.TokenSecretField, func(rawObj client.Object) []string {
		token := rawObj.(*nginxpmoperatoriov1.Token)
		if token.Spec.Secret.SecretName == "" {
			return nil
		}
		return []string{token.Spec.Secret.SecretName}
	})
}

// CreateObjectMapFunc creates a map function for finding objects by field
func (h *ControllerSetupHelper) CreateObjectMapFunc(field string, listType client.ObjectList) func(context.Context, client.Object) []reconcile.Request {
	return func(ctx context.Context, object client.Object) []reconcile.Request {
		listOps := &client.ListOptions{
			FieldSelector: fields.OneTermEqualSelector(field, object.GetName()),
		}

		err := h.mgr.GetClient().List(ctx, listType, listOps)
		if err != nil {
			h.logger.Error(err, "Failed to list objects for map function", "field", field)
			return []reconcile.Request{}
		}

		var requests []reconcile.Request
		
		// Use reflection to get items from the list
		switch list := listType.(type) {
		case *nginxpmoperatoriov1.ProxyHostList:
			requests = make([]reconcile.Request, len(list.Items))
			for i, item := range list.Items {
				requests[i] = reconcile.Request{
					NamespacedName: types.NamespacedName{
						Name:      item.GetName(),
						Namespace: item.GetNamespace(),
					},
				}
			}
		case *nginxpmoperatoriov1.StreamList:
			requests = make([]reconcile.Request, len(list.Items))
			for i, item := range list.Items {
				requests[i] = reconcile.Request{
					NamespacedName: types.NamespacedName{
						Name:      item.GetName(),
						Namespace: item.GetNamespace(),
					},
				}
			}
		case *nginxpmoperatoriov1.TokenList:
			requests = make([]reconcile.Request, len(list.Items))
			for i, item := range list.Items {
				requests[i] = reconcile.Request{
					NamespacedName: types.NamespacedName{
						Name:      item.GetName(),
						Namespace: item.GetNamespace(),
					},
				}
			}
		}

		return requests
	}
}

// SetupCommonWatches sets up common watches for a controller
func (h *ControllerSetupHelper) SetupCommonWatches(
	controllerBuilder *builder.Builder,
	resourceType client.Object,
	listType client.ObjectList,
) *builder.Builder {
	h.logger.Debug("Setting up common watches", "resourceType", resourceType.GetObjectKind().GroupVersionKind().Kind)

	return controllerBuilder.
		Owns(&nginxpmoperatoriov1.Token{}).
		Owns(&nginxpmoperatoriov1.CustomCertificate{}).
		Owns(&nginxpmoperatoriov1.LetsEncryptCertificate{}).
		Owns(&corev1.Service{}).
		Watches(
			&nginxpmoperatoriov1.Token{},
			handler.EnqueueRequestsFromMapFunc(h.CreateObjectMapFunc(config.TokenField, listType)),
			builder.WithPredicates(predicate.ResourceVersionChangedPredicate{}),
		).
		Watches(
			&nginxpmoperatoriov1.CustomCertificate{},
			handler.EnqueueRequestsFromMapFunc(h.CreateObjectMapFunc(config.CustomCertificateField, listType)),
			builder.WithPredicates(predicate.ResourceVersionChangedPredicate{}),
		).
		Watches(
			&nginxpmoperatoriov1.LetsEncryptCertificate{},
			handler.EnqueueRequestsFromMapFunc(h.CreateObjectMapFunc(config.LetsEncryptCertificateField, listType)),
			builder.WithPredicates(predicate.ResourceVersionChangedPredicate{}),
		).
		Watches(
			&corev1.Service{},
			handler.EnqueueRequestsFromMapFunc(h.CreateObjectMapFunc(config.ForwardServiceField, listType)),
			builder.WithPredicates(predicate.ResourceVersionChangedPredicate{}),
		)
}

// SetupProxyHostWatches sets up watches specific to ProxyHost
func (h *ControllerSetupHelper) SetupProxyHostWatches(controllerBuilder *builder.Builder) *builder.Builder {
	return h.SetupCommonWatches(controllerBuilder, &nginxpmoperatoriov1.ProxyHost{}, &nginxpmoperatoriov1.ProxyHostList{}).
		Owns(&nginxpmoperatoriov1.AccessList{}).
		Watches(
			&nginxpmoperatoriov1.AccessList{},
			handler.EnqueueRequestsFromMapFunc(h.CreateObjectMapFunc(config.AccessListField, &nginxpmoperatoriov1.ProxyHostList{})),
			builder.WithPredicates(predicate.ResourceVersionChangedPredicate{}),
		)
}

// SetupStreamWatches sets up watches specific to Stream
func (h *ControllerSetupHelper) SetupStreamWatches(controllerBuilder *builder.Builder) *builder.Builder {
	return h.SetupCommonWatches(controllerBuilder, &nginxpmoperatoriov1.Stream{}, &nginxpmoperatoriov1.StreamList{})
}
