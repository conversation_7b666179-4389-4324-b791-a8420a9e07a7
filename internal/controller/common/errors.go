/*
Copyright 2024.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package common

import (
	"fmt"
	"strings"
)

// ErrorType represents different types of errors that can occur
type ErrorType string

const (
	ErrorTypeValidation    ErrorType = "Validation"
	ErrorTypeConnection    ErrorType = "Connection"
	ErrorTypeNotFound      ErrorType = "NotFound"
	ErrorTypeConflict      ErrorType = "Conflict"
	ErrorTypeTimeout       ErrorType = "Timeout"
	ErrorTypeUnauthorized  ErrorType = "Unauthorized"
	ErrorTypeInternal      ErrorType = "Internal"
	ErrorTypeConfiguration ErrorType = "Configuration"
)

// OperatorError represents a structured error with context
type OperatorError struct {
	Type      ErrorType
	Message   string
	Cause     error
	Component string
	Resource  string
	Operation string
}

// Error implements the error interface
func (e *OperatorError) Error() string {
	var parts []string
	
	if e.Component != "" {
		parts = append(parts, fmt.Sprintf("component=%s", e.Component))
	}
	
	if e.Resource != "" {
		parts = append(parts, fmt.Sprintf("resource=%s", e.Resource))
	}
	
	if e.Operation != "" {
		parts = append(parts, fmt.Sprintf("operation=%s", e.Operation))
	}
	
	if e.Type != "" {
		parts = append(parts, fmt.Sprintf("type=%s", e.Type))
	}
	
	context := ""
	if len(parts) > 0 {
		context = fmt.Sprintf("[%s] ", strings.Join(parts, ", "))
	}
	
	message := e.Message
	if e.Cause != nil {
		message = fmt.Sprintf("%s: %v", message, e.Cause)
	}
	
	return fmt.Sprintf("%s%s", context, message)
}

// Unwrap returns the underlying cause
func (e *OperatorError) Unwrap() error {
	return e.Cause
}

// Is checks if the error matches the target
func (e *OperatorError) Is(target error) bool {
	if t, ok := target.(*OperatorError); ok {
		return e.Type == t.Type
	}
	return false
}

// NewError creates a new OperatorError
func NewError(errorType ErrorType, message string) *OperatorError {
	return &OperatorError{
		Type:    errorType,
		Message: message,
	}
}

// NewErrorWithCause creates a new OperatorError with a cause
func NewErrorWithCause(errorType ErrorType, message string, cause error) *OperatorError {
	return &OperatorError{
		Type:    errorType,
		Message: message,
		Cause:   cause,
	}
}

// WithComponent adds component context to the error
func (e *OperatorError) WithComponent(component string) *OperatorError {
	e.Component = component
	return e
}

// WithResource adds resource context to the error
func (e *OperatorError) WithResource(resource string) *OperatorError {
	e.Resource = resource
	return e
}

// WithOperation adds operation context to the error
func (e *OperatorError) WithOperation(operation string) *OperatorError {
	e.Operation = operation
	return e
}

// Common error constructors
func ValidationError(message string) *OperatorError {
	return NewError(ErrorTypeValidation, message)
}

func ValidationErrorWithCause(message string, cause error) *OperatorError {
	return NewErrorWithCause(ErrorTypeValidation, message, cause)
}

func ConnectionError(message string) *OperatorError {
	return NewError(ErrorTypeConnection, message)
}

func ConnectionErrorWithCause(message string, cause error) *OperatorError {
	return NewErrorWithCause(ErrorTypeConnection, message, cause)
}

func NotFoundError(message string) *OperatorError {
	return NewError(ErrorTypeNotFound, message)
}

func NotFoundErrorWithCause(message string, cause error) *OperatorError {
	return NewErrorWithCause(ErrorTypeNotFound, message, cause)
}

func ConflictError(message string) *OperatorError {
	return NewError(ErrorTypeConflict, message)
}

func ConflictErrorWithCause(message string, cause error) *OperatorError {
	return NewErrorWithCause(ErrorTypeConflict, message, cause)
}

func TimeoutError(message string) *OperatorError {
	return NewError(ErrorTypeTimeout, message)
}

func TimeoutErrorWithCause(message string, cause error) *OperatorError {
	return NewErrorWithCause(ErrorTypeTimeout, message, cause)
}

func UnauthorizedError(message string) *OperatorError {
	return NewError(ErrorTypeUnauthorized, message)
}

func UnauthorizedErrorWithCause(message string, cause error) *OperatorError {
	return NewErrorWithCause(ErrorTypeUnauthorized, message, cause)
}

func InternalError(message string) *OperatorError {
	return NewError(ErrorTypeInternal, message)
}

func InternalErrorWithCause(message string, cause error) *OperatorError {
	return NewErrorWithCause(ErrorTypeInternal, message, cause)
}

func ConfigurationError(message string) *OperatorError {
	return NewError(ErrorTypeConfiguration, message)
}

func ConfigurationErrorWithCause(message string, cause error) *OperatorError {
	return NewErrorWithCause(ErrorTypeConfiguration, message, cause)
}

// IsRetryable determines if an error should trigger a retry
func IsRetryable(err error) bool {
	if operatorErr, ok := err.(*OperatorError); ok {
		switch operatorErr.Type {
		case ErrorTypeConnection, ErrorTypeTimeout, ErrorTypeInternal:
			return true
		case ErrorTypeValidation, ErrorTypeNotFound, ErrorTypeUnauthorized, ErrorTypeConfiguration:
			return false
		case ErrorTypeConflict:
			return true // Conflicts might be resolved on retry
		default:
			return false
		}
	}
	return true // Default to retryable for unknown errors
}

// GetRetryDelay returns the appropriate retry delay for an error type
func GetRetryDelay(err error) string {
	if operatorErr, ok := err.(*OperatorError); ok {
		switch operatorErr.Type {
		case ErrorTypeConnection, ErrorTypeTimeout:
			return "fast" // Quick retry for connection issues
		case ErrorTypeConflict:
			return "default" // Normal retry for conflicts
		case ErrorTypeInternal:
			return "slow" // Slower retry for internal errors
		default:
			return "default"
		}
	}
	return "default"
}
