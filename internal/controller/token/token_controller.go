/*
Copyright 2024.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package token

import (
	"context"
	"fmt"
	"time"

	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/meta"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/fields"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/builder"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/handler"
	"sigs.k8s.io/controller-runtime/pkg/predicate"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"

	nginxpmoperatoriov1 "github.com/paradoxe35/nginxpm-operator/api/v1"
	"github.com/paradoxe35/nginxpm-operator/internal/config"
	"github.com/paradoxe35/nginxpm-operator/internal/controller"
	"github.com/paradoxe35/nginxpm-operator/internal/controller/common"
	"github.com/paradoxe35/nginxpm-operator/pkg/nginxpm"
	"github.com/paradoxe35/nginxpm-operator/pkg/util"
)

// TokenReconciler reconciles a Token object
type TokenReconciler struct {
	client.Client
	Scheme *runtime.Scheme
}

// +kubebuilder:rbac:groups=nginxpm-operator.io,resources=tokens,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=nginxpm-operator.io,resources=tokens/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=nginxpm-operator.io,resources=tokens/finalizers,verbs=update
// +kubebuilder:rbac:groups=core,resources=secrets,verbs=get;list;watch

// Reconcile is part of the main kubernetes reconciliation loop which aims to
// move the current state of the cluster closer to the desired state.
//
// For more details, check Reconcile and its Result here:
// - https://nginxpm.go.dev/sigs.k8s.io/controller-runtime@v0.19.0/pkg/reconcile
func (r *TokenReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	// Create logging context with improved structure
	logCtx := common.NewLogContextForRequest(ctx, "TokenController", req)
	logCtx.ReconciliationStarted()

	token := &nginxpmoperatoriov1.Token{}

	// Fetch the Token instance
	err := r.Get(ctx, req.NamespacedName, token)
	if err != nil {
		if apierrors.IsNotFound(err) {
			logCtx.Info("Token resource not found. Ignoring since object must be deleted")
			return ctrl.Result{}, nil
		}
		logCtx.Error(err, "Failed to get token")
		return ctrl.Result{RequeueAfter: config.DefaultRequeueAfter}, err
	}

	// Set initial status if no conditions exist
	if len(token.Status.Conditions) == 0 {
		controller.UpdateStatus(ctx, r.Client, token, req.NamespacedName, func() {
			meta.SetStatusCondition(&token.Status.Conditions, metav1.Condition{
				Status:             metav1.ConditionUnknown,
				Type:               config.ConditionTypeReconciling,
				Reason:             "Reconciling",
				Message:            "Starting reconciliation",
				LastTransitionTime: metav1.Now(),
			})
		})
		return ctrl.Result{}, nil
	}

	// Initialize NGINX PM client
	nginxpmClient, err := r.initNginxPMClient(ctx, req, token)
	if err != nil {
		logCtx.ClientInitializationFailed(err, token.Spec.Endpoint)

		// Set error status
		controller.UpdateStatus(ctx, r.Client, token, req.NamespacedName, func() {
			meta.SetStatusCondition(&token.Status.Conditions, metav1.Condition{
				Status:             metav1.ConditionFalse,
				Type:               config.ConditionTypeError,
				Reason:             config.EventReasonInitNginxPMClient,
				Message:            err.Error(),
				LastTransitionTime: metav1.Now(),
			})
		})

		return ctrl.Result{RequeueAfter: config.DefaultRequeueAfter}, err
	}

	// Update token status if changed
	if token.Status.Token == nil || *token.Status.Token != nginxpmClient.Token {
		if err := controller.UpdateStatus(ctx, r.Client, token, req.NamespacedName, func() {
			token.Status.Token = &nginxpmClient.Token
			token.Status.Expires = &metav1.Time{Time: nginxpmClient.Expires}
		}); err != nil {
			logCtx.Error(err, "Failed to update Token status")
			return ctrl.Result{RequeueAfter: config.DefaultRequeueAfter}, err
		}
	}

	logCtx.Info("Token created successfully", "expires", nginxpmClient.Expires.String())

	// Calculate requeue time based on token expiration
	requeueAfter := nginxpmClient.Expires.UTC().Sub(metav1.Now().UTC())
	if requeueAfter <= 0 {
		requeueAfter = config.DefaultRequeueAfter
	}

	// Set success status
	controller.UpdateStatus(ctx, r.Client, token, req.NamespacedName, func() {
		meta.SetStatusCondition(&token.Status.Conditions, metav1.Condition{
			Status:             metav1.ConditionTrue,
			Type:               config.ConditionTypeReady,
			Reason:             config.EventReasonTokenCreated,
			Message:            fmt.Sprintf("Token created and expires at: %s", nginxpmClient.Expires.String()),
			LastTransitionTime: metav1.Now(),
		})
	})

	logCtx.ReconciliationCompleted(requeueAfter.String())
	return ctrl.Result{RequeueAfter: requeueAfter}, nil
}

func (r *TokenReconciler) initNginxPMClient(ctx context.Context, req reconcile.Request, token *nginxpmoperatoriov1.Token) (*nginxpm.Client, error) {
	logCtx := common.NewLogContextForRequest(ctx, "TokenController", req)

	// Get the secret resource associated with the token
	secret := &corev1.Secret{}
	secretName := token.Spec.Secret.SecretName
	if err := r.Get(ctx, types.NamespacedName{Namespace: req.Namespace, Name: secretName}, secret); err != nil {
		return nil, common.NotFoundErrorWithCause(
			fmt.Sprintf("Secret %s not found", secretName), err,
		).WithComponent("TokenController").WithOperation("GetSecret")
	}

	// Validate secret data
	identity, ok := secret.Data["identity"]
	if !ok {
		return nil, common.ValidationError("identity field missing from secret").
			WithComponent("TokenController").WithResource(secretName)
	}

	secretDataValue, ok := secret.Data["secret"]
	if !ok {
		return nil, common.ValidationError("secret field missing from secret").
			WithComponent("TokenController").WithResource(secretName)
	}

	// Determine if we have a valid existing token
	var nginxpmClient *nginxpm.Client
	expiredAt := token.Status.Expires
	hasValidToken := token.Status.Token != nil && expiredAt != nil && expiredAt.UTC().After(time.Now().UTC())

	// Use existing token if valid
	if hasValidToken {
		logCtx.Info("Using existing token from status")
		nginxpmClient = nginxpm.NewClientFromToken(util.NewHttpClient(), token)

		// Verify connection with existing token
		if err := nginxpmClient.CheckConnection(); err != nil {
			logCtx.Error(err, "Connection failed with existing token")
			return nil, common.ConnectionErrorWithCause("Failed to connect with existing token", err).
				WithComponent("TokenController").WithOperation("CheckConnection")
		}
	}

	// Create new token if needed
	if !hasValidToken {
		logCtx.Info("Creating new NGINX PM client and token")

		// Create new client
		nginxpmClient = nginxpm.NewClient(util.NewHttpClient(), token.Spec.Endpoint)

		// Verify connection to endpoint
		if err := nginxpmClient.CheckConnection(); err != nil {
			logCtx.Error(err, "Connection failed to NGINX PM endpoint", "endpoint", token.Spec.Endpoint)
			return nil, common.ConnectionErrorWithCause("Failed to connect to NGINX PM endpoint", err).
				WithComponent("TokenController").WithOperation("CheckConnection")
		}

		// Create new token using credentials
		if err := nginxpm.CreateClientToken(nginxpmClient, string(identity), string(secretDataValue)); err != nil {
			logCtx.Error(err, "Failed to create token from credentials")
			return nil, common.UnauthorizedErrorWithCause("Failed to create token from credentials", err).
				WithComponent("TokenController").WithOperation("CreateToken")
		}

		logCtx.Info("New token created successfully", "expires", nginxpmClient.Expires.String())
	}

	return nginxpmClient, nil
}

// SetupWithManager sets up the controller with the Manager.
func (r *TokenReconciler) SetupWithManager(mgr ctrl.Manager) error {
	// Setup field indexer for token secret references
	if err := mgr.GetFieldIndexer().IndexField(
		context.Background(),
		&nginxpmoperatoriov1.Token{},
		config.TokenSecretField,
		func(rawObj client.Object) []string {
			token := rawObj.(*nginxpmoperatoriov1.Token)
			if token.Spec.Secret.SecretName == "" {
				return nil
			}
			return []string{token.Spec.Secret.SecretName}
		}); err != nil {
		return err
	}

	return ctrl.NewControllerManagedBy(mgr).
		For(&nginxpmoperatoriov1.Token{}).
		Owns(&corev1.Secret{}).
		Watches(
			&corev1.Secret{},
			handler.EnqueueRequestsFromMapFunc(r.findObjectsForSecret),
			builder.WithPredicates(predicate.ResourceVersionChangedPredicate{}),
		).
		Named("token").
		Complete(r)
}

func (r *TokenReconciler) findObjectsForSecret(ctx context.Context, secret client.Object) []reconcile.Request {
	attachedTokens := &nginxpmoperatoriov1.TokenList{}

	listOps := &client.ListOptions{
		FieldSelector: fields.OneTermEqualSelector(config.TokenSecretField, secret.GetName()),
		Namespace:     secret.GetNamespace(),
	}

	err := r.List(ctx, attachedTokens, listOps)
	if err != nil {
		// Log error but don't fail the reconciliation
		common.NewLogContext(ctx, "TokenController").Error(err, "Failed to list tokens for secret", "secretName", secret.GetName())
		return []reconcile.Request{}
	}

	requests := make([]reconcile.Request, len(attachedTokens.Items))
	for i, item := range attachedTokens.Items {
		requests[i] = reconcile.Request{
			NamespacedName: types.NamespacedName{
				Name:      item.GetName(),
				Namespace: item.GetNamespace(),
			},
		}
	}

	return requests
}
