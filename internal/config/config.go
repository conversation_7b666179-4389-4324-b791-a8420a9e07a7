/*
Copyright 2024.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package config

import (
	"time"
)

// Condition types for status management
const (
	ConditionTypeReady        = "Ready"
	ConditionTypeReconciling  = "Reconciling"
	ConditionTypeError        = "Error"
	ConditionTypeDegraded     = "Degraded"
	ConditionTypeProgressing  = "Progressing"
	ConditionTypeAvailable    = "Available"
	ConditionTypeNotAvailable = "NotAvailable"
)

// Token-related constants
const (
	TokenDefaultName      = "token-nginxpm"
	TokenSystemNamespace  = "nginxpm-operator-system"
	TokenDefaultNamespace = "default"
)

// Reconciliation timing constants
const (
	DefaultRequeueAfter = time.Minute
	FastRequeueAfter    = 10 * time.Second
	SlowRequeueAfter    = 5 * time.Minute
	RetryRequeueAfter   = 30 * time.Second
)

// Finalizer constants
const (
	ProxyHostFinalizer              = "proxyhost.nginxpm-operator.io/finalizers"
	StreamFinalizer                 = "stream.nginxpm-operator.io/finalizers"
	TokenFinalizer                  = "token.nginxpm-operator.io/finalizers"
	CustomCertificateFinalizer      = "customcertificate.nginxpm-operator.io/finalizers"
	LetsEncryptCertificateFinalizer = "letsencryptcertificate.nginxpm-operator.io/finalizers"
	AccessListFinalizer             = "accesslist.nginxpm-operator.io/finalizers"
)

// Field indexer constants
const (
	TokenField                      = ".spec.token.name"
	CustomCertificateField          = ".spec.ssl.customCertificate.name"
	LetsEncryptCertificateField     = ".spec.ssl.letsEncryptCertificate.name"
	AccessListField                 = ".spec.accessList.name"
	ForwardServiceField             = ".spec.forward.service.name"
	TokenSecretField                = ".spec.secret.secretName"
)

// NGINX upstream configuration constants
const (
	KeepAliveCount    = 32               // Reasonable pool size per worker
	KeepAliveTimeout  = 60 * time.Second // Nginx default is 60s
	KeepAliveRequests = 1000             // Nginx default is 1000 since 1.19.10 (prev 100)
	MaxFails          = 3                // Times to fail before marking down
	FailTimeout       = 30 * time.Second // Duration to mark down
)

// Event reasons
const (
	EventReasonInitNginxPMClient     = "InitNginxPMClient"
	EventReasonCreateOrUpdate        = "CreateOrUpdate"
	EventReasonDelete                = "Delete"
	EventReasonValidation            = "Validation"
	EventReasonCertificate           = "Certificate"
	EventReasonForward               = "Forward"
	EventReasonDomainValidation      = "DomainValidation"
	EventReasonTokenCreated          = "TokenCreated"
	EventReasonTokenExpired          = "TokenExpired"
	EventReasonConnectionFailed      = "ConnectionFailed"
	EventReasonReconciliationSuccess = "ReconciliationSuccess"
	EventReasonReconciliationFailed  = "ReconciliationFailed"
)

// HTTP client configuration
const (
	DefaultHTTPTimeout = 30 * time.Second
	DefaultRetryCount  = 3
)

// OperatorConfig holds the configuration for the operator
type OperatorConfig struct {
	// MetricsAddr is the address the metrics endpoint binds to
	MetricsAddr string
	
	// ProbeAddr is the address the probe endpoint binds to
	ProbeAddr string
	
	// EnableLeaderElection enables leader election for controller manager
	EnableLeaderElection bool
	
	// SecureMetrics determines if metrics endpoint is served securely
	SecureMetrics bool
	
	// EnableHTTP2 determines if HTTP/2 is enabled for metrics and webhook servers
	EnableHTTP2 bool
	
	// Development mode for logging
	Development bool
	
	// LeaderElectionID for the controller manager
	LeaderElectionID string
	
	// HTTPTimeout for nginx client requests
	HTTPTimeout time.Duration
	
	// RetryCount for failed operations
	RetryCount int
}

// DefaultOperatorConfig returns the default configuration
func DefaultOperatorConfig() *OperatorConfig {
	return &OperatorConfig{
		MetricsAddr:          "0",
		ProbeAddr:            ":8081",
		EnableLeaderElection: false,
		SecureMetrics:        true,
		EnableHTTP2:          false,
		Development:          true,
		LeaderElectionID:     "9510bba9.nginxpm-operator.io",
		HTTPTimeout:          DefaultHTTPTimeout,
		RetryCount:           DefaultRetryCount,
	}
}

// ReconcilerConfig holds common configuration for reconcilers
type ReconcilerConfig struct {
	// RequeueAfter is the default requeue duration
	RequeueAfter time.Duration
	
	// FastRequeue is used for quick retries
	FastRequeue time.Duration
	
	// SlowRequeue is used for less frequent checks
	SlowRequeue time.Duration
	
	// MaxRetries is the maximum number of retries for failed operations
	MaxRetries int
}

// DefaultReconcilerConfig returns the default reconciler configuration
func DefaultReconcilerConfig() *ReconcilerConfig {
	return &ReconcilerConfig{
		RequeueAfter: DefaultRequeueAfter,
		FastRequeue:  FastRequeueAfter,
		SlowRequeue:  SlowRequeueAfter,
		MaxRetries:   3,
	}
}
