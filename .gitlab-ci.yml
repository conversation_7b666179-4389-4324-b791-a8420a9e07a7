stages:
  - manifests-dist
  - release
  - build-images

manifests-dist:
  image: golang:1.23
  stage: manifests-dist
  rules:
    - if: $CI_COMMIT_TAG
  script:
    - apt-get update && apt-get install -y make
    - make kustomize
    - make build-installer IMG=$CI_REGISTRY_IMAGE:$CI_COMMIT_TAG
  artifacts:
    name: $CI_COMMIT_TAG
    expire_in: never
    paths:
      - dist/install.yaml

############## Create release #####################

release:
  stage: release
  image: registry.gitlab.com/gitlab-org/release-cli:latest
  needs:
    - job: manifests-dist
      artifacts: true
  script:
    - echo "Create Release $TAG"
    - echo $CI_JOB_ID
  rules:
    - if: $CI_COMMIT_TAG
  artifacts:
    name: $CI_COMMIT_TAG
    expire_in: never
    paths:
      - dist/install.yaml
  release:
    assets:
      links:
        - name: install.yaml
          url: $CI_JOB_URL/artifacts/raw/dist/install.yaml
    name: "$CI_COMMIT_TAG"
    tag_name: "$CI_COMMIT_TAG"
    description: "$CI_COMMIT_MESSAGE"

# ############# Build docker images #########################

build-images:
  image: docker:27
  stage: build-images
  needs: [release]
  services:
    - docker:27-dind
  rules:
    - if: $CI_COMMIT_TAG

  before_script:
    - apk update && apk add make bash --no-cache
    - echo "user:$CI_REGISTRY_USER -- pwd:$CI_REGISTRY_PASSWORD -- registry:$CI_REGISTRY"
    - echo "$CI_REGISTRY_PASSWORD" | docker login --username $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
    - echo "$CI_REGISTRY_IMAGE"
    - echo "$CI_COMMIT_TAG"
    - echo "$CI_COMMIT_TAG_MESSAGE"
  script:
    - echo "Build Images, and create release"
    - export IMG_TAG=$CI_REGISTRY_IMAGE:$CI_COMMIT_TAG
    - export IMG_LATEST=$CI_REGISTRY_IMAGE:latest

    - ./buildx --push -t $IMG_TAG -t $IMG_LATEST --provenance=false
