name: CI/CD Pipeline

on:
  push:
    tags: [v**]

jobs:
  manifests-dist:
    if: "!contains(github.event.head_commit.message, '[skip ci]')"
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: "1.23"

      - name: Install dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y make

      - name: Build manifests
        run: |
          make kustomize
          make build-installer IMG=ghcr.io/${{ github.repository }}:${{ github.ref_name }}

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: ${{ github.ref_name }}
          path: dist/install.yaml
          retention-days: 0

  release:
    if: "!contains(github.event.head_commit.message, '[skip ci]')"
    needs: manifests-dist
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          fetch-depth: 0

      - name: Download artifacts
        uses: actions/download-artifact@v4
        with:
          name: ${{ github.ref_name }}
          path: dist

      - name: Update install.yaml in tag
        run: |
          git config user.name "GitHub Actions"
          git config user.email "<EMAIL>"

          # Check if file exists in the repository
          if [ -f dist/install.yaml ]; then
            git add dist/install.yaml
          else
            echo "dist/install.yaml not found"
            exit 1
          fi

          # Only commit and push if there are changes
          if ! git diff-index --quiet HEAD --; then
            git commit -m "Update install.yaml for ${{ github.ref_name }} [skip ci]"
            git tag -f ${{ github.ref_name }}
            git push origin ${{ github.ref_name }} --force
          else
            echo "No changes to commit"
          fi

      - name: Create Release
        id: create_release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref }}
          release_name: ${{ github.ref }}
          body: ${{ github.event.head_commit.message }}

      - name: Upload Release Asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }}
          asset_path: ./dist/install.yaml
          asset_name: install.yaml
          asset_content_type: text/yaml

  build-images:
    if: "!contains(github.event.head_commit.message, '[skip ci]')"
    needs: release
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build and push multi-architecture Docker image
        run: |
          export IMG_TAG=ghcr.io/${{ github.repository }}:${{ github.ref_name }}
          export IMG_LATEST=ghcr.io/${{ github.repository }}:latest

          ./buildx --push -t $IMG_TAG -t $IMG_LATEST
