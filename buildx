#!/bin/bash

# Colors
BLUE='\E[1;34m'
CYAN='\E[1;36m'
GREEN='\E[1;32m'
RED='\E[1;31m'
RESET='\E[0m'
YELLOW='\E[1;33m'


# PLATFORMS defines the target platforms for the manager image be built to provide support to multiple
# architectures. (i.e. make docker-buildx IMG=myregistry/mypoperator:0.0.1). To use this option you need to:
# - be able to use docker buildx. More info: https://docs.docker.com/build/buildx/
# - have enabled BuildKit. More info: https://docs.docker.com/develop/develop-images/build_enhancements/
# - be able to push the image to your registry (i.e. if you do not set a valid value via IMG=<myregistry/image:<tag>> then the export will fail)
# To adequately provide solutions that are compatible with multiple platforms, you should consider using this option.

# copy existing Dockerfile and insert --platform=${BUILDPLATFORM} into Dockerfile.cross, and preserve the original Dockerfile
if grep -q "Alpine" /etc/os-release 2>/dev/null; then
    echo "Detected Alpine Linux, using compatible sed command"
    sed '1 s/^FROM/FROM --platform=${BUILDPLATFORM}/' Dockerfile > Dockerfile.cross
else
    echo "Using GNU sed command for Ubuntu/other distributions"
    sed -e '1 s/\(^FROM\)/FROM --platform=\$\{BUILDPLATFORM\}/; t' -e ' 1,// s//FROM --platform=\$\{BUILDPLATFORM\}/' Dockerfile > Dockerfile.cross
fi

BUILDX_NAME=nginxpm-operator-builder
PLATFORMS=linux/arm64,linux/amd64,linux/s390x,linux/ppc64le


docker buildx create --name ${BUILDX_NAME}
docker buildx use ${BUILDX_NAME}

docker buildx build --platform=${PLATFORMS} --pull --progress plain -f Dockerfile.cross $@ .

rc=$?

docker buildx rm ${BUILDX_NAME}
rm Dockerfile.cross

echo -e "${BLUE}❯ ${GREEN}Multiarch build Complete${RESET}"
exit $rc