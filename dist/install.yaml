apiVersion: v1
kind: Namespace
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: nginxpm-operator
    control-plane: controller-manager
  name: nginxpm-operator-system
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.1
  name: accesslists.nginxpm-operator.io
spec:
  group: nginxpm-operator.io
  names:
    kind: AccessList
    listKind: AccessListList
    plural: accesslists
    singular: accesslist
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.id
      name: ID
      type: integer
    - jsonPath: .spec.name
      name: Name
      type: string
    - jsonPath: .status.proxyHostCount
      name: Proxy Host Count
      type: integer
    name: v1
    schema:
      openAPIV3Schema:
        description: AccessList is the Schema for the accesslists API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: AccessListSpec defines the desired state of AccessList.
            properties:
              authorizations:
                description: Basic Authorization via Nginx HTTP Basic Authentication
                  (https://nginx.org/en/docs/http/ngx_http_auth_basic_module.html)
                items:
                  properties:
                    password:
                      description: |-
                        Password to be used for authentication with the access list service.
                        Must be between 1 and 255 characters.
                      maxLength: 255
                      minLength: 1
                      type: string
                    username:
                      description: |-
                        Username to be used for authentication with the access list service.
                        Must be between 1 and 255 characters.
                      maxLength: 255
                      minLength: 1
                      type: string
                  required:
                  - password
                  - username
                  type: object
                type: array
              clients:
                description: IP Address Whitelist/Blacklist via Nginx HTTP Access
                  (https://nginx.org/en/docs/http/ngx_http_access_module.html)
                items:
                  properties:
                    address:
                      description: Address (IPv4 IP/SUBNET) for authentication use
                      pattern: ^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(\/([0-9]|[1-2][0-9]|3[0-2]))?$
                      type: string
                    directive:
                      description: Directive for Authentication Use
                      enum:
                      - allow
                      - deny
                      type: string
                  required:
                  - address
                  - directive
                  type: object
                type: array
              name:
                description: Access list name that will be displayed from remote  Nginx
                  Proxy Manager instance
                maxLength: 255
                minLength: 1
                type: string
              passAuth:
                description: Authorization to host should only be enabled if the host
                  has basic authentication enabled.
                enum:
                - true
                - false
                type: boolean
              satisfyAny:
                description: If set true, allow access if at least one condition is
                  met when multiple authentication or access control methods are defined.
                enum:
                - true
                - false
                type: boolean
              token:
                description: Token resource, if not provided, the operator will try
                  to find a token with `token-nginxpm` name in the same namespace
                  as the proxyhost is created or in the `nginxpm-operator-system`
                  namespace or in the `default` namespace
                properties:
                  name:
                    description: Name of the token resource
                    type: string
                  namespace:
                    description: Namespace of the token resource
                    pattern: ^[a-z]([-a-z0-9]*[a-z0-9])?$
                    type: string
                required:
                - name
                type: object
            required:
            - name
            type: object
          status:
            description: AccessListStatus defines the observed state of AccessList.
            properties:
              conditions:
                description: Represents the observations of a AccessListStatus's current
                  state.
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              id:
                description: AccessList ID from remote  Nginx Proxy Manager instance
                type: integer
              proxyHostCount:
                description: Number of proxy hosts associated with this AccessList
                type: integer
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.1
  name: customcertificates.nginxpm-operator.io
spec:
  group: nginxpm-operator.io
  names:
    kind: CustomCertificate
    listKind: CustomCertificateList
    plural: customcertificates
    singular: customcertificate
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.id
      name: ID
      type: integer
    - jsonPath: .status.expiresOn
      name: ExpiresOn
      type: string
    - jsonPath: .status.status
      name: Status
      type: string
    name: v1
    schema:
      openAPIV3Schema:
        description: CustomCertificate is the Schema for the customcertificates API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: CustomCertificateSpec defines the desired state of CustomCertificate
            properties:
              certificate:
                description: Certificate credential secret name
                properties:
                  secret:
                    description: Secret resource holds certificate values
                    properties:
                      name:
                        description: Name of the secret resource
                        type: string
                    required:
                    - name
                    type: object
                required:
                - secret
                type: object
              niceName:
                description: NiceName of the CustomCertificate (If not provided, the
                  resource name will be used)
                maxLength: 64
                type: string
              token:
                description: Token resource, if not provided, the operator will try
                  to find a token with `token-nginxpm` name in the same namespace
                  as the proxyhost is created or in the `nginxpm-operator-system`
                  namespace or in the `default` namespace
                properties:
                  name:
                    description: Name of the token resource
                    type: string
                  namespace:
                    description: Namespace of the token resource
                    pattern: ^[a-z]([-a-z0-9]*[a-z0-9])?$
                    type: string
                required:
                - name
                type: object
            required:
            - certificate
            type: object
          status:
            description: CustomCertificateStatus defines the observed state of CustomCertificate
            properties:
              conditions:
                description: Represents the observations of a CustomCertificateStatus's
                  current state.
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              expiresOn:
                description: Expiration time of the certificate
                type: string
              id:
                description: CustomCertificateStatus ID from remote  Nginx Proxy Manager
                  instance
                type: integer
              status:
                description: Status of the certificate
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.1
  name: letsencryptcertificates.nginxpm-operator.io
spec:
  group: nginxpm-operator.io
  names:
    kind: LetsEncryptCertificate
    listKind: LetsEncryptCertificateList
    plural: letsencryptcertificates
    singular: letsencryptcertificate
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.id
      name: ID
      type: integer
    - jsonPath: .spec.domainNames
      name: DomainNames
      type: string
    - jsonPath: .status.bound
      name: Bound
      type: boolean
    - jsonPath: .status.expiresOn
      name: ExpiresOn
      type: string
    name: v1
    schema:
      openAPIV3Schema:
        description: LetsEncryptCertificate is the Schema for the letsencryptcertificates
          API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: LetsEncryptCertificateSpec defines the desired state of LetsEncryptCertificate
            properties:
              dnsChallenge:
                description: Use DNS challenge
                properties:
                  propagationSeconds:
                    default: 0
                    description: Propagation seconds
                    type: integer
                  provider:
                    description: DNS Provider to use
                    enum:
                    - acmedns
                    - aliyun
                    - azure
                    - bunny
                    - cloudflare
                    - cloudns
                    - cloudxns
                    - constellix
                    - corenetworks
                    - cpanel
                    - desec
                    - duckdns
                    - digitalocean
                    - directadmin
                    - dnsimple
                    - dnsmadeeasy
                    - dnsmulti
                    - dnspod
                    - domainoffensive
                    - domeneshop
                    - dynu
                    - easydns
                    - eurodns
                    - freedns
                    - gandi
                    - godaddy
                    - google
                    - googledomains
                    - he
                    - hetzner
                    - infomaniak
                    - inwx
                    - ionos
                    - ispconfig
                    - isset
                    - joker
                    - linode
                    - loopia
                    - luadns
                    - namecheap
                    - netcup
                    - njalla
                    - nsone
                    - oci
                    - ovh
                    - plesk
                    - porkbun
                    - powerdns
                    - regru
                    - rfc2136
                    - route53
                    - strato
                    - timeweb
                    - transip
                    - tencentcloud
                    - vultr
                    - websupport
                    type: string
                  providerCredentials:
                    description: Provider credentials
                    properties:
                      secret:
                        description: Secret resource holds dns challenge provider
                          credentials
                        properties:
                          name:
                            description: Name of the secret resource
                            type: string
                        required:
                        - name
                        type: object
                    required:
                    - secret
                    type: object
                    x-kubernetes-preserve-unknown-fields: true
                required:
                - provider
                - providerCredentials
                type: object
              domainNames:
                description: Domain Names to request a certificate for
                items:
                  pattern: ^(\*\.)?[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z]{2,}$
                  type: string
                maxItems: 10
                minItems: 1
                type: array
              letsEncryptEmail:
                description: LetsEncrypt Email address to request a certificate for
                format: email
                pattern: ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$
                type: string
              token:
                description: Token resource, if not provided, the operator will try
                  to find a token with `token-nginxpm` name in the same namespace
                  as the proxyhost is created or in the `nginxpm-operator-system`
                  namespace or in the `default` namespace
                properties:
                  name:
                    description: Name of the token resource
                    type: string
                  namespace:
                    description: Namespace of the token resource
                    pattern: ^[a-z]([-a-z0-9]*[a-z0-9])?$
                    type: string
                required:
                - name
                type: object
            required:
            - domainNames
            - letsEncryptEmail
            type: object
          status:
            description: LetsEncryptCertificateStatus defines the observed state of
              LetsEncryptCertificate
            properties:
              bound:
                default: false
                description: Whether the LetsEncryptCertificate was bound with an
                  existing certificate
                type: boolean
              conditions:
                description: Represents the observations of a LetsEncryptCertificate's
                  current state.
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              domainNames:
                description: |-
                  Duplicated Domain Names in status, since once the certificate is created for these domain names
                  the spec.domainNames will never changed
                items:
                  type: string
                maxItems: 10
                minItems: 1
                type: array
              expiresOn:
                description: Expiration time of the certificate
                type: string
              id:
                description: LetsEncryptCertificate ID from remote  Nginx Proxy Manager
                  instance
                type: integer
            required:
            - domainNames
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.1
  name: proxyhosts.nginxpm-operator.io
spec:
  group: nginxpm-operator.io
  names:
    kind: ProxyHost
    listKind: ProxyHostList
    plural: proxyhosts
    singular: proxyhost
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.id
      name: ID
      type: integer
    - jsonPath: .status.online
      name: Online
      type: boolean
    - jsonPath: .status.certificateId
      name: CertificateId
      type: string
    - jsonPath: .spec.domainNames
      name: Domains
      type: string
    - jsonPath: .status.bound
      name: Bound
      type: boolean
    name: v1
    schema:
      openAPIV3Schema:
        description: ProxyHost is the Schema for the proxyhosts API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: ProxyHostSpec defines the desired state of ProxyHost
            properties:
              accessList:
                description: AccessList to add to the proxyhost
                properties:
                  accessListId:
                    description: If you know ID of an access list, you can put it
                      here
                    type: integer
                  name:
                    description: The access list resource name
                    type: string
                  namespace:
                    description: Namespace of the access list resource
                    pattern: ^[a-z]([-a-z0-9]*[a-z0-9])?$
                    type: string
                type: object
              bindExisting:
                default: true
                description: If set to true (the default), it will bind and update
                  an existing remote proxy host if the domain names match; otherwise,
                  it will create a new one.
                type: boolean
              blockExploits:
                default: true
                description: BlockExploits is the flag to enable or disable blocking
                  exploits, default is true
                type: boolean
              cachingEnabled:
                default: false
                description: CachingEnabled is the flag to enable or disable caching,
                  default is false
                type: boolean
              customLocations:
                description: CustomLocations is the list of custom locations to add
                  to the proxyhost
                items:
                  properties:
                    forward:
                      description: The Service forward configuration for the custom
                        location
                      properties:
                        advancedConfig:
                          description: AdvancedConfig is the advanced configuration
                            for the proxyhost, at your own risk
                          type: string
                        hosts:
                          description: List of your forward hosts; if specified, this
                            will take priority over the service.
                          items:
                            properties:
                              hostName:
                                description: The host to forward to (This must be
                                  a valid DNS name or IP address)
                                maxLength: 255
                                minLength: 1
                                pattern: ^((([a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)\.)*[a-zA-Z]{2,63}|((25[0-5]|2[0-4][0-9]|[0-1]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[0-1]?[0-9][0-9]?)|(([0-9a-fA-F]{1,4}:){7}([0-9a-fA-F]{1,4}|:))|(::([0-9a-fA-F]{1,4}:){0,6}[0-9a-fA-F]{1,4}))$
                                type: string
                              hostPort:
                                description: Service Target Port is the port to forward
                                  to
                                format: int32
                                maximum: 65535
                                minimum: 1
                                type: integer
                            required:
                            - hostName
                            - hostPort
                            type: object
                          type: array
                        path:
                          description: Add a path for sub-folder forwarding
                          pattern: ^\/([a-zA-Z0-9._~-]+\/?)*$
                          type: string
                        scheme:
                          description: Scheme is the scheme to use for the forwarding,
                            (http or https)
                          enum:
                          - http
                          - https
                          type: string
                        service:
                          description: Service resource reference to be forwarded
                            to
                          properties:
                            name:
                              description: |-
                                Name of the service resource to forward to
                                IP and port of the service will be used as the forwarding target
                                Only ClusterIP and LoadBalancer services are supported
                              maxLength: 255
                              minLength: 1
                              type: string
                            namespace:
                              description: Namespace of the service resource to forward
                                to
                              pattern: ^[a-z]([-a-z0-9]*[a-z0-9])?$
                              type: string
                            port:
                              description: Force forwarding to a known service port
                              format: int32
                              maximum: 65535
                              minimum: 1
                              type: integer
                          required:
                          - name
                          type: object
                      required:
                      - scheme
                      type: object
                    locationPath:
                      description: Define location Location path
                      pattern: ^\/([a-zA-Z0-9._~-]+\/?)*$
                      type: string
                  required:
                  - forward
                  - locationPath
                  type: object
                type: array
              domainNames:
                description: DomainNames is the list of domain names to add to the
                  proxyhost
                items:
                  pattern: ^(\*\.)?[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z]{2,}$
                  type: string
                maxItems: 10
                minItems: 1
                type: array
              forward:
                description: The Service forward configuration for the proxyhost
                properties:
                  advancedConfig:
                    description: AdvancedConfig is the advanced configuration for
                      the proxyhost, at your own risk
                    type: string
                  hosts:
                    description: List of your forward hosts; if specified, this will
                      take priority over the service.
                    items:
                      properties:
                        hostName:
                          description: The host to forward to (This must be a valid
                            DNS name or IP address)
                          maxLength: 255
                          minLength: 1
                          pattern: ^((([a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)\.)*[a-zA-Z]{2,63}|((25[0-5]|2[0-4][0-9]|[0-1]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[0-1]?[0-9][0-9]?)|(([0-9a-fA-F]{1,4}:){7}([0-9a-fA-F]{1,4}|:))|(::([0-9a-fA-F]{1,4}:){0,6}[0-9a-fA-F]{1,4}))$
                          type: string
                        hostPort:
                          description: Service Target Port is the port to forward
                            to
                          format: int32
                          maximum: 65535
                          minimum: 1
                          type: integer
                      required:
                      - hostName
                      - hostPort
                      type: object
                    type: array
                  path:
                    description: Add a path for sub-folder forwarding
                    pattern: ^\/([a-zA-Z0-9._~-]+\/?)*$
                    type: string
                  scheme:
                    description: Scheme is the scheme to use for the forwarding, (http
                      or https)
                    enum:
                    - http
                    - https
                    type: string
                  service:
                    description: Service resource reference to be forwarded to
                    properties:
                      name:
                        description: |-
                          Name of the service resource to forward to
                          IP and port of the service will be used as the forwarding target
                          Only ClusterIP and LoadBalancer services are supported
                        maxLength: 255
                        minLength: 1
                        type: string
                      namespace:
                        description: Namespace of the service resource to forward
                          to
                        pattern: ^[a-z]([-a-z0-9]*[a-z0-9])?$
                        type: string
                      port:
                        description: Force forwarding to a known service port
                        format: int32
                        maximum: 65535
                        minimum: 1
                        type: integer
                    required:
                    - name
                    type: object
                required:
                - scheme
                type: object
              ssl:
                description: Ssl configuration for the proxyhost, default is autoCertificateRequest:true
                properties:
                  autoCertificateRequest:
                    default: false
                    description: When true, will request a certificate from Let's
                      Encrypt automatically
                    type: boolean
                  certificateId:
                    description: |-
                      Bind existing certificate id to the stream
                      CustomCertificate has priority over LetsencryptCertificate and  CustomCertificate
                    type: integer
                  customCertificate:
                    description: |-
                      Custom Certificate name managed by the customCertificate resource
                      CustomCertificate has priority over LetsencryptCertificate
                    properties:
                      name:
                        description: Name of the custom certificate resource
                        type: string
                      namespace:
                        description: Namespace of the custom certificate resource
                        pattern: ^[a-z]([-a-z0-9]*[a-z0-9])?$
                        type: string
                    required:
                    - name
                    type: object
                  hstsEnabled:
                    default: false
                    description: Enable HSTS, default is false
                    type: boolean
                  hstsSubdomains:
                    default: false
                    description: Enable HSTS subdomains, default is false
                    type: boolean
                  http2Support:
                    default: true
                    description: Enable http2 support, default is true
                    type: boolean
                  letsEncryptCertificate:
                    description: Letsencrypt Certificate name managed by the letsencryptCertificate
                      resource
                    properties:
                      name:
                        description: Name of the letsencrypt certificate resource
                        type: string
                      namespace:
                        description: Namespace of the letsencrypt certificate resource
                        pattern: ^[a-z]([-a-z0-9]*[a-z0-9])?$
                        type: string
                    required:
                    - name
                    type: object
                  letsEncryptEmail:
                    description: LetsEncrypt Email address to request a certificate
                      for
                    pattern: ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$
                    type: string
                  sslForced:
                    default: true
                    description: Force SSL https, redirect http to https. default
                      is true
                    type: boolean
                type: object
              token:
                description: Token resource, if not provided, the operator will try
                  to find a token with `token-nginxpm` name in the same namespace
                  as the proxyhost is created or in the `nginxpm-operator-system`
                  namespace or in the `default` namespace
                properties:
                  name:
                    description: Name of the token resource
                    type: string
                  namespace:
                    description: Namespace of the token resource
                    pattern: ^[a-z]([-a-z0-9]*[a-z0-9])?$
                    type: string
                required:
                - name
                type: object
              websocketSupport:
                default: true
                description: WebsocketSupport is the flag to enable or disable websocket
                  support, default is true
                type: boolean
            required:
            - domainNames
            - forward
            type: object
          status:
            description: ProxyHostStatus defines the observed state of ProxyHost
            properties:
              bound:
                default: false
                description: Whether the ProxyHost was bound with an existing proxyhost
                type: boolean
              certificateId:
                description: ProxyHost certificate ID from remote  Nginx Proxy Manager
                  instance
                type: integer
              conditions:
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              id:
                description: ProxyHost ID from remote  Nginx Proxy Manager instance
                type: integer
              online:
                description: Online status from remote Nginx Proxy Manager instance
                enum:
                - true
                - false
                type: boolean
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.1
  name: streams.nginxpm-operator.io
spec:
  group: nginxpm-operator.io
  names:
    kind: Stream
    listKind: StreamList
    plural: streams
    singular: stream
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.id
      name: ID
      type: integer
    - jsonPath: .status.online
      name: Online
      type: boolean
    - jsonPath: .status.incomingPort
      name: Incoming
      type: integer
    - jsonPath: .status.forwardingPort
      name: Forwarding
      type: integer
    - jsonPath: .spec.forward.tcpForwarding
      name: TCP
      type: boolean
    - jsonPath: .spec.forward.udpForwarding
      name: UDP
      type: boolean
    name: v1
    schema:
      openAPIV3Schema:
        description: Stream is the Schema for the streams API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: StreamSpec defines the desired state of Stream.
            properties:
              forward:
                description: Stream forward configuration
                properties:
                  hosts:
                    description: List of your forward hosts; if specified, this will
                      take priority over the service.
                    items:
                      properties:
                        hostName:
                          description: The host to forward to (This must be a valid
                            DNS name or IP address)
                          maxLength: 255
                          minLength: 1
                          pattern: ^((([a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)\.)*[a-zA-Z]{2,63}|((25[0-5]|2[0-4][0-9]|[0-1]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[0-1]?[0-9][0-9]?)|(([0-9a-fA-F]{1,4}:){7}([0-9a-fA-F]{1,4}|:))|(::([0-9a-fA-F]{1,4}:){0,6}[0-9a-fA-F]{1,4}))$
                          type: string
                        hostPort:
                          description: Service Target Port is the port to forward
                            to
                          format: int32
                          maximum: 65535
                          minimum: 1
                          type: integer
                      required:
                      - hostName
                      - hostPort
                      type: object
                    type: array
                  service:
                    description: Service resource reference to be forwarded to
                    properties:
                      name:
                        description: |-
                          Name of the service resource to forward to
                          IP and port of the service will be used as the forwarding target
                          Only ClusterIP and LoadBalancer services are supported
                        maxLength: 255
                        minLength: 1
                        type: string
                      namespace:
                        description: Namespace of the service resource to forward
                          to
                        pattern: ^[a-z]([-a-z0-9]*[a-z0-9])?$
                        type: string
                      port:
                        description: Force forwarding to a known service port
                        format: int32
                        maximum: 65535
                        minimum: 1
                        type: integer
                    required:
                    - name
                    type: object
                  tcpForwarding:
                    description: Has TCP Forwarding
                    enum:
                    - true
                    - false
                    type: boolean
                  udpForwarding:
                    description: Has UDP Forwarding
                    enum:
                    - true
                    - false
                    type: boolean
                type: object
              incomingPort:
                description: Incoming Port
                maximum: 65535
                minimum: 1
                type: integer
              overwriteIncomingPortWithForwardPort:
                description: If True the incoming port will be overwritten with the
                  forward port
                enum:
                - true
                - false
                type: boolean
              ssl:
                description: Ssl configuration for the stream
                properties:
                  certificateId:
                    description: |-
                      Bind existing certificate id to the stream
                      CustomCertificate has priority over LetsencryptCertificate and  CustomCertificate
                    type: integer
                  customCertificate:
                    description: |-
                      Custom Certificate name managed by the customCertificate resource
                      CustomCertificate has priority over LetsencryptCertificate
                    properties:
                      name:
                        description: Name of the custom certificate resource
                        type: string
                      namespace:
                        description: Namespace of the custom certificate resource
                        pattern: ^[a-z]([-a-z0-9]*[a-z0-9])?$
                        type: string
                    required:
                    - name
                    type: object
                  letsEncryptCertificate:
                    description: Letsencrypt Certificate name managed by the letsencryptCertificate
                      resource
                    properties:
                      name:
                        description: Name of the letsencrypt certificate resource
                        type: string
                      namespace:
                        description: Namespace of the letsencrypt certificate resource
                        pattern: ^[a-z]([-a-z0-9]*[a-z0-9])?$
                        type: string
                    required:
                    - name
                    type: object
                type: object
              token:
                description: Token resource, if not provided, the operator will try
                  to find a token with `token-nginxpm` name in the same namespace
                  as the proxyhost is created or in the `nginxpm-operator-system`
                  namespace or in the `default` namespace
                properties:
                  name:
                    description: Name of the token resource
                    type: string
                  namespace:
                    description: Namespace of the token resource
                    pattern: ^[a-z]([-a-z0-9]*[a-z0-9])?$
                    type: string
                required:
                - name
                type: object
            required:
            - forward
            - incomingPort
            type: object
          status:
            description: StreamStatus defines the observed state of Stream.
            properties:
              conditions:
                description: Represents the observations of a Stream's current state.
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              forwardingPort:
                description: Forwarding port
                type: integer
              id:
                description: Stream ID from remote Nginx Proxy Manager instance
                type: integer
              incomingPort:
                description: Incoming port
                type: integer
              online:
                description: Online status from remote Nginx Proxy Manager instance
                enum:
                - true
                - false
                type: boolean
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.1
  name: tokens.nginxpm-operator.io
spec:
  group: nginxpm-operator.io
  names:
    kind: Token
    listKind: TokenList
    plural: tokens
    singular: token
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .spec.secret.secretName
      name: Secret
      type: string
    - jsonPath: .status.expires
      name: Expires
      type: string
    name: v1
    schema:
      openAPIV3Schema:
        description: Token is the Schema for the tokens API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: TokenSpec defines the desired state of Token
            properties:
              endpoint:
                description: Endpoint of a Nginx Proxy manager instance
                maxLength: 255
                minLength: 1
                pattern: ^(https?):\/\/([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(:[0-9]{1,5})?$
                type: string
              secret:
                description: Secret resource reference to add to the token cr
                properties:
                  secretName:
                    description: SecretName is the name of the secret resource to
                      add to the token cr
                    maxLength: 255
                    minLength: 1
                    type: string
                required:
                - secretName
                type: object
            required:
            - endpoint
            - secret
            type: object
          status:
            description: TokenStatus defines the observed state of Token
            properties:
              conditions:
                description: |-
                  Represents the observations of a Token's current state.
                  For further information see: https://github.com/kubernetes/community/blob/master/contributors/devel/sig-architecture/api-conventions.md#typical-status-properties
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              expires:
                description: Expiration time of the token, value is generated from
                  controller reconcile
                format: date-time
                type: string
              token:
                description: Authentication Token, this is generated from controller
                  reconcile
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: nginxpm-operator
  name: nginxpm-operator-controller-manager
  namespace: nginxpm-operator-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: nginxpm-operator
  name: nginxpm-operator-leader-election-role
  namespace: nginxpm-operator-system
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - coordination.k8s.io
  resources:
  - leases
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: nginxpm-operator
  name: nginxpm-operator-accesslist-admin-role
rules:
- apiGroups:
  - nginxpm-operator.io
  resources:
  - accesslists
  verbs:
  - '*'
- apiGroups:
  - nginxpm-operator.io
  resources:
  - accesslists/status
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: nginxpm-operator
  name: nginxpm-operator-accesslist-editor-role
rules:
- apiGroups:
  - nginxpm-operator.io
  resources:
  - accesslists
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - nginxpm-operator.io
  resources:
  - accesslists/status
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: nginxpm-operator
  name: nginxpm-operator-accesslist-viewer-role
rules:
- apiGroups:
  - nginxpm-operator.io
  resources:
  - accesslists
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - nginxpm-operator.io
  resources:
  - accesslists/status
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: nginxpm-operator
  name: nginxpm-operator-customcertificate-editor-role
rules:
- apiGroups:
  - nginxpm-operator.io
  resources:
  - customcertificates
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - nginxpm-operator.io
  resources:
  - customcertificates/status
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: nginxpm-operator
  name: nginxpm-operator-customcertificate-viewer-role
rules:
- apiGroups:
  - nginxpm-operator.io
  resources:
  - customcertificates
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - nginxpm-operator.io
  resources:
  - customcertificates/status
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: nginxpm-operator
  name: nginxpm-operator-letsencryptcertificate-editor-role
rules:
- apiGroups:
  - nginxpm-operator.io
  resources:
  - letsencryptcertificates
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - nginxpm-operator.io
  resources:
  - letsencryptcertificates/status
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: nginxpm-operator
  name: nginxpm-operator-letsencryptcertificate-viewer-role
rules:
- apiGroups:
  - nginxpm-operator.io
  resources:
  - letsencryptcertificates
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - nginxpm-operator.io
  resources:
  - letsencryptcertificates/status
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: nginxpm-operator-manager-role
rules:
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch
- apiGroups:
  - ""
  resources:
  - nodes
  - pods
  - secrets
  - services
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - nodes/status
  - pods/status
  - services/status
  verbs:
  - get
- apiGroups:
  - nginxpm-operator.io
  resources:
  - accesslist
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - nginxpm-operator.io
  resources:
  - accesslist/status
  verbs:
  - get
- apiGroups:
  - nginxpm-operator.io
  resources:
  - accesslists
  - customcertificates
  - letsencryptcertificates
  - proxyhosts
  - streams
  - tokens
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - nginxpm-operator.io
  resources:
  - accesslists/finalizers
  - customcertificates/finalizers
  - letsencryptcertificates/finalizers
  - proxyhosts/finalizers
  - streams/finalizers
  - tokens/finalizers
  verbs:
  - update
- apiGroups:
  - nginxpm-operator.io
  resources:
  - accesslists/status
  - customcertificates/status
  - letsencryptcertificates/status
  - proxyhosts/status
  - streams/status
  - tokens/status
  verbs:
  - get
  - patch
  - update
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: nginxpm-operator-metrics-auth-role
rules:
- apiGroups:
  - authentication.k8s.io
  resources:
  - tokenreviews
  verbs:
  - create
- apiGroups:
  - authorization.k8s.io
  resources:
  - subjectaccessreviews
  verbs:
  - create
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: nginxpm-operator-metrics-reader
rules:
- nonResourceURLs:
  - /metrics
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: nginxpm-operator
  name: nginxpm-operator-proxyhost-editor-role
rules:
- apiGroups:
  - nginxpm-operator.io
  resources:
  - proxyhosts
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - nginxpm-operator.io
  resources:
  - proxyhosts/status
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: nginxpm-operator
  name: nginxpm-operator-proxyhost-viewer-role
rules:
- apiGroups:
  - nginxpm-operator.io
  resources:
  - proxyhosts
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - nginxpm-operator.io
  resources:
  - proxyhosts/status
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: nginxpm-operator
  name: nginxpm-operator-stream-admin-role
rules:
- apiGroups:
  - nginxpm-operator.io
  resources:
  - streams
  verbs:
  - '*'
- apiGroups:
  - nginxpm-operator.io
  resources:
  - streams/status
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: nginxpm-operator
  name: nginxpm-operator-stream-editor-role
rules:
- apiGroups:
  - nginxpm-operator.io
  resources:
  - streams
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - nginxpm-operator.io
  resources:
  - streams/status
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: nginxpm-operator
  name: nginxpm-operator-stream-viewer-role
rules:
- apiGroups:
  - nginxpm-operator.io
  resources:
  - streams
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - nginxpm-operator.io
  resources:
  - streams/status
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: nginxpm-operator
  name: nginxpm-operator-token-editor-role
rules:
- apiGroups:
  - nginxpm-operator.io
  resources:
  - tokens
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - nginxpm-operator.io
  resources:
  - tokens/status
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: nginxpm-operator
  name: nginxpm-operator-token-viewer-role
rules:
- apiGroups:
  - nginxpm-operator.io
  resources:
  - tokens
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - nginxpm-operator.io
  resources:
  - tokens/status
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: nginxpm-operator
  name: nginxpm-operator-leader-election-rolebinding
  namespace: nginxpm-operator-system
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: nginxpm-operator-leader-election-role
subjects:
- kind: ServiceAccount
  name: nginxpm-operator-controller-manager
  namespace: nginxpm-operator-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: nginxpm-operator
  name: nginxpm-operator-manager-rolebinding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: nginxpm-operator-manager-role
subjects:
- kind: ServiceAccount
  name: nginxpm-operator-controller-manager
  namespace: nginxpm-operator-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: nginxpm-operator-metrics-auth-rolebinding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: nginxpm-operator-metrics-auth-role
subjects:
- kind: ServiceAccount
  name: nginxpm-operator-controller-manager
  namespace: nginxpm-operator-system
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: nginxpm-operator
    control-plane: controller-manager
  name: nginxpm-operator-controller-manager-metrics-service
  namespace: nginxpm-operator-system
spec:
  ports:
  - name: https
    port: 8443
    protocol: TCP
    targetPort: 8443
  selector:
    control-plane: controller-manager
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: nginxpm-operator
    control-plane: controller-manager
  name: nginxpm-operator-controller-manager
  namespace: nginxpm-operator-system
spec:
  replicas: 1
  selector:
    matchLabels:
      control-plane: controller-manager
  template:
    metadata:
      annotations:
        kubectl.kubernetes.io/default-container: manager
      labels:
        control-plane: controller-manager
    spec:
      containers:
      - args:
        - --metrics-bind-address=:8443
        - --leader-elect
        - --health-probe-bind-address=:8081
        command:
        - /manager
        image: ghcr.io/paradoxe35/nginxpm-operator:latest
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8081
          initialDelaySeconds: 15
          periodSeconds: 20
        name: manager
        readinessProbe:
          httpGet:
            path: /readyz
            port: 8081
          initialDelaySeconds: 5
          periodSeconds: 10
        resources:
          limits:
            cpu: 500m
            memory: 128Mi
          requests:
            cpu: 10m
            memory: 64Mi
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
      securityContext:
        runAsNonRoot: true
      serviceAccountName: nginxpm-operator-controller-manager
      terminationGracePeriodSeconds: 10
