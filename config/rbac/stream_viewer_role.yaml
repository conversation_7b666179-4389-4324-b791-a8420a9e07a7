# This rule is not used by the project nginxpm-operator itself.
# It is provided to allow the cluster admin to help manage permissions for users.
#
# Grants read-only access to nginxpm-operator.io resources.
# This role is intended for users who need visibility into these resources
# without permissions to modify them. It is ideal for monitoring purposes and limited-access viewing.

apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: nginxpm-operator
    app.kubernetes.io/managed-by: kustomize
  name: stream-viewer-role
rules:
- apiGroups:
  - nginxpm-operator.io
  resources:
  - streams
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - nginxpm-operator.io
  resources:
  - streams/status
  verbs:
  - get
