# This rule is not used by the project nginxpm-operator itself.
# It is provided to allow the cluster admin to help manage permissions for users.
#
# Grants full permissions ('*') over nginxpm-operator.io.
# This role is intended for users authorized to modify roles and bindings within the cluster,
# enabling them to delegate specific permissions to other users or groups as needed.

apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: nginxpm-operator
    app.kubernetes.io/managed-by: kustomize
  name: stream-admin-role
rules:
- apiGroups:
  - nginxpm-operator.io
  resources:
  - streams
  verbs:
  - '*'
- apiGroups:
  - nginxpm-operator.io
  resources:
  - streams/status
  verbs:
  - get
