# permissions for end users to edit letsencryptcertificates.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: nginxpm-operator
    app.kubernetes.io/managed-by: kustomize
  name: letsencryptcertificate-editor-role
rules:
- apiGroups:
  - nginxpm-operator.io
  resources:
  - letsencryptcertificates
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - nginxpm-operator.io
  resources:
  - letsencryptcertificates/status
  verbs:
  - get
