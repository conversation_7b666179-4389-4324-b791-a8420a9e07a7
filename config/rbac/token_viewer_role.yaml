# permissions for end users to view tokens.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: nginxpm-operator
    app.kubernetes.io/managed-by: kustomize
  name: token-viewer-role
rules:
- apiGroups:
  - nginxpm-operator.io
  resources:
  - tokens
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - nginxpm-operator.io
  resources:
  - tokens/status
  verbs:
  - get
