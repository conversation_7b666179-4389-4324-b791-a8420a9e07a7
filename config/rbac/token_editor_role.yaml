# permissions for end users to edit tokens.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: nginxpm-operator
    app.kubernetes.io/managed-by: kustomize
  name: token-editor-role
rules:
- apiGroups:
  - nginxpm-operator.io
  resources:
  - tokens
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - nginxpm-operator.io
  resources:
  - tokens/status
  verbs:
  - get
