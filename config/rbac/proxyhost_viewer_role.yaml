# permissions for end users to view proxyhosts.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: nginxpm-operator
    app.kubernetes.io/managed-by: kustomize
  name: proxyhost-viewer-role
rules:
- apiGroups:
  - nginxpm-operator.io
  resources:
  - proxyhosts
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - nginxpm-operator.io
  resources:
  - proxyhosts/status
  verbs:
  - get
