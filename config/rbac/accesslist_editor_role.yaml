# This rule is not used by the project nginxpm-operator itself.
# It is provided to allow the cluster admin to help manage permissions for users.
#
# Grants permissions to create, update, and delete resources within the nginxpm-operator.io.
# This role is intended for users who need to manage these resources
# but should not control RBAC or manage permissions for others.

apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: nginxpm-operator
    app.kubernetes.io/managed-by: kustomize
  name: accesslist-editor-role
rules:
- apiGroups:
  - nginxpm-operator.io
  resources:
  - accesslists
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - nginxpm-operator.io
  resources:
  - accesslists/status
  verbs:
  - get
