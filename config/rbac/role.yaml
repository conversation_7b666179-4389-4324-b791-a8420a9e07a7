---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: manager-role
rules:
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch
- apiGroups:
  - ""
  resources:
  - nodes
  - pods
  - secrets
  - services
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - nodes/status
  - pods/status
  - services/status
  verbs:
  - get
- apiGroups:
  - nginxpm-operator.io
  resources:
  - accesslist
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - nginxpm-operator.io
  resources:
  - accesslist/status
  verbs:
  - get
- apiGroups:
  - nginxpm-operator.io
  resources:
  - accesslists
  - customcertificates
  - letsencryptcertificates
  - proxyhosts
  - streams
  - tokens
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - nginxpm-operator.io
  resources:
  - accesslists/finalizers
  - customcertificates/finalizers
  - letsencryptcertificates/finalizers
  - proxyhosts/finalizers
  - streams/finalizers
  - tokens/finalizers
  verbs:
  - update
- apiGroups:
  - nginxpm-operator.io
  resources:
  - accesslists/status
  - customcertificates/status
  - letsencryptcertificates/status
  - proxyhosts/status
  - streams/status
  - tokens/status
  verbs:
  - get
  - patch
  - update
