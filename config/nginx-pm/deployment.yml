apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginxpm
  namespace: nginxpm
spec:
  replicas: 1
  selector:
    matchLabels:
      app: nginxpm
  template:
    metadata:
      labels:
        app: nginxpm
    spec:
      containers:
        - name: nginxpm
          image: jc21/nginx-proxy-manager:2
          resources:
            limits:
              memory: "128Mi"
              cpu: "200m"
          ports:
            - containerPort: 81
            - containerPort: 80
            - containerPort: 443
          volumeMounts:
            - mountPath: "/data"
              name: nginxpm-data
            - mountPath: "/etc/letsencrypt"
              name: nginxpm-letsencrypt
      volumes:
        - name: nginxpm-data
          persistentVolumeClaim:
            claimName: nginxpm-data
        - name: nginxpm-letsencrypt
          persistentVolumeClaim:
            claimName: nginxpm-letsencrypt
