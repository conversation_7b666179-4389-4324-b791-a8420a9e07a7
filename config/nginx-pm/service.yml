apiVersion: v1
kind: Service
metadata:
  name: nginxpm-proxy
  namespace: nginxpm
spec:
  ports:
    - name: proxy
      port: 80
      protocol: TCP
      targetPort: 80
    - name: proxy-ssl
      port: 443
      protocol: TCP
      targetPort: 443
  selector:
    app: nginxpm
  externalTrafficPolicy: Local
  type: LoadBalancer

---
apiVersion: v1
kind: Service
metadata:
  name: nginxpm-admin-ui
  namespace: nginxpm
spec:
  ports:
    - name: admin-ui
      port: 81
      protocol: TCP
      targetPort: 81
  selector:
    app: nginxpm
