---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.1
  name: accesslists.nginxpm-operator.io
spec:
  group: nginxpm-operator.io
  names:
    kind: AccessList
    listKind: AccessListList
    plural: accesslists
    singular: accesslist
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.id
      name: ID
      type: integer
    - jsonPath: .spec.name
      name: Name
      type: string
    - jsonPath: .status.proxyHostCount
      name: Proxy Host Count
      type: integer
    name: v1
    schema:
      openAPIV3Schema:
        description: AccessList is the Schema for the accesslists API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: AccessListSpec defines the desired state of AccessList.
            properties:
              authorizations:
                description: Basic Authorization via Nginx HTTP Basic Authentication
                  (https://nginx.org/en/docs/http/ngx_http_auth_basic_module.html)
                items:
                  properties:
                    password:
                      description: |-
                        Password to be used for authentication with the access list service.
                        Must be between 1 and 255 characters.
                      maxLength: 255
                      minLength: 1
                      type: string
                    username:
                      description: |-
                        Username to be used for authentication with the access list service.
                        Must be between 1 and 255 characters.
                      maxLength: 255
                      minLength: 1
                      type: string
                  required:
                  - password
                  - username
                  type: object
                type: array
              clients:
                description: IP Address Whitelist/Blacklist via Nginx HTTP Access
                  (https://nginx.org/en/docs/http/ngx_http_access_module.html)
                items:
                  properties:
                    address:
                      description: Address (IPv4 IP/SUBNET) for authentication use
                      pattern: ^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(\/([0-9]|[1-2][0-9]|3[0-2]))?$
                      type: string
                    directive:
                      description: Directive for Authentication Use
                      enum:
                      - allow
                      - deny
                      type: string
                  required:
                  - address
                  - directive
                  type: object
                type: array
              passAuth:
                description: Authorization to host should only be enabled if the host
                  has basic authentication enabled.
                enum:
                - true
                - false
                type: boolean
              satisfyAny:
                description: If set true, allow access if at least one condition is
                  met when multiple authentication or access control methods are defined.
                enum:
                - true
                - false
                type: boolean
              token:
                description: Token resource, if not provided, the operator will try
                  to find a token with `token-nginxpm` name in the same namespace
                  as the proxyhost is created or in the `nginxpm-operator-system`
                  namespace or in the `default` namespace
                properties:
                  name:
                    description: Name of the token resource
                    type: string
                  namespace:
                    description: Namespace of the token resource
                    pattern: ^[a-z]([-a-z0-9]*[a-z0-9])?$
                    type: string
                required:
                - name
                type: object
            type: object
          status:
            description: AccessListStatus defines the observed state of AccessList.
            properties:
              conditions:
                description: Represents the observations of a AccessListStatus's current
                  state.
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              id:
                description: AccessList ID from remote  Nginx Proxy Manager instance
                type: integer
              proxyHostCount:
                description: Number of proxy hosts associated with this AccessList
                type: integer
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
