---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.1
  name: letsencryptcertificates.nginxpm-operator.io
spec:
  group: nginxpm-operator.io
  names:
    kind: LetsEncryptCertificate
    listKind: LetsEncryptCertificateList
    plural: letsencryptcertificates
    singular: letsencryptcertificate
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.id
      name: ID
      type: integer
    - jsonPath: .spec.domainNames
      name: DomainNames
      type: string
    - jsonPath: .status.bound
      name: Bound
      type: boolean
    - jsonPath: .status.expiresOn
      name: ExpiresOn
      type: string
    name: v1
    schema:
      openAPIV3Schema:
        description: LetsEncryptCertificate is the Schema for the letsencryptcertificates
          API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: LetsEncryptCertificateSpec defines the desired state of LetsEncryptCertificate
            properties:
              dnsChallenge:
                description: Use DNS challenge
                properties:
                  propagationSeconds:
                    default: 0
                    description: Propagation seconds
                    type: integer
                  provider:
                    description: DNS Provider to use
                    enum:
                    - acmedns
                    - aliyun
                    - azure
                    - bunny
                    - cloudflare
                    - cloudns
                    - cloudxns
                    - constellix
                    - corenetworks
                    - cpanel
                    - desec
                    - duckdns
                    - digitalocean
                    - directadmin
                    - dnsimple
                    - dnsmadeeasy
                    - dnsmulti
                    - dnspod
                    - domainoffensive
                    - domeneshop
                    - dynu
                    - easydns
                    - eurodns
                    - freedns
                    - gandi
                    - godaddy
                    - google
                    - googledomains
                    - he
                    - hetzner
                    - infomaniak
                    - inwx
                    - ionos
                    - ispconfig
                    - isset
                    - joker
                    - linode
                    - loopia
                    - luadns
                    - namecheap
                    - netcup
                    - njalla
                    - nsone
                    - oci
                    - ovh
                    - plesk
                    - porkbun
                    - powerdns
                    - regru
                    - rfc2136
                    - route53
                    - strato
                    - timeweb
                    - transip
                    - tencentcloud
                    - vultr
                    - websupport
                    type: string
                  providerCredentials:
                    description: Provider credentials
                    properties:
                      secret:
                        description: Secret resource holds dns challenge provider
                          credentials
                        properties:
                          name:
                            description: Name of the secret resource
                            type: string
                        required:
                        - name
                        type: object
                    required:
                    - secret
                    type: object
                    x-kubernetes-preserve-unknown-fields: true
                required:
                - provider
                - providerCredentials
                type: object
              domainNames:
                description: Domain Names to request a certificate for
                items:
                  pattern: ^(\*\.)?[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z]{2,}$
                  type: string
                maxItems: 10
                minItems: 1
                type: array
              letsEncryptEmail:
                description: LetsEncrypt Email address to request a certificate for
                format: email
                pattern: ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$
                type: string
              token:
                description: Token resource, if not provided, the operator will try
                  to find a token with `token-nginxpm` name in the same namespace
                  as the proxyhost is created or in the `nginxpm-operator-system`
                  namespace or in the `default` namespace
                properties:
                  name:
                    description: Name of the token resource
                    type: string
                  namespace:
                    description: Namespace of the token resource
                    pattern: ^[a-z]([-a-z0-9]*[a-z0-9])?$
                    type: string
                required:
                - name
                type: object
            required:
            - domainNames
            - letsEncryptEmail
            type: object
          status:
            description: LetsEncryptCertificateStatus defines the observed state of
              LetsEncryptCertificate
            properties:
              bound:
                default: false
                description: Whether the LetsEncryptCertificate was bound with an
                  existing certificate
                type: boolean
              conditions:
                description: Represents the observations of a LetsEncryptCertificate's
                  current state.
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              domainNames:
                description: |-
                  Duplicated Domain Names in status, since once the certificate is created for these domain names
                  the spec.domainNames will never changed
                items:
                  type: string
                maxItems: 10
                minItems: 1
                type: array
              expiresOn:
                description: Expiration time of the certificate
                type: string
              id:
                description: LetsEncryptCertificate ID from remote  Nginx Proxy Manager
                  instance
                type: integer
            required:
            - domainNames
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
