---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.1
  name: tokens.nginxpm-operator.io
spec:
  group: nginxpm-operator.io
  names:
    kind: Token
    listKind: TokenList
    plural: tokens
    singular: token
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .spec.secret.secretName
      name: Secret
      type: string
    - jsonPath: .status.expires
      name: Expires
      type: string
    name: v1
    schema:
      openAPIV3Schema:
        description: Token is the Schema for the tokens API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: TokenSpec defines the desired state of Token
            properties:
              endpoint:
                description: Endpoint of a Nginx Proxy manager instance
                maxLength: 255
                minLength: 1
                pattern: ^(https?):\/\/([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(:[0-9]{1,5})?$
                type: string
              secret:
                description: Secret resource reference to add to the token cr
                properties:
                  secretName:
                    description: SecretName is the name of the secret resource to
                      add to the token cr
                    maxLength: 255
                    minLength: 1
                    type: string
                required:
                - secretName
                type: object
            required:
            - endpoint
            - secret
            type: object
          status:
            description: TokenStatus defines the observed state of Token
            properties:
              conditions:
                description: |-
                  Represents the observations of a Token's current state.
                  For further information see: https://github.com/kubernetes/community/blob/master/contributors/devel/sig-architecture/api-conventions.md#typical-status-properties
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              expires:
                description: Expiration time of the token, value is generated from
                  controller reconcile
                format: date-time
                type: string
              token:
                description: Authentication Token, this is generated from controller
                  reconcile
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
