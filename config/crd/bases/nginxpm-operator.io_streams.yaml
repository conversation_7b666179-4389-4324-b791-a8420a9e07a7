---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.1
  name: streams.nginxpm-operator.io
spec:
  group: nginxpm-operator.io
  names:
    kind: Stream
    listKind: StreamList
    plural: streams
    singular: stream
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.id
      name: ID
      type: integer
    - jsonPath: .status.online
      name: Online
      type: boolean
    - jsonPath: .status.incomingPort
      name: Incoming
      type: integer
    - jsonPath: .status.forwardingPort
      name: Forwarding
      type: integer
    - jsonPath: .spec.forward.tcpForwarding
      name: TCP
      type: boolean
    - jsonPath: .spec.forward.udpForwarding
      name: UDP
      type: boolean
    name: v1
    schema:
      openAPIV3Schema:
        description: Stream is the Schema for the streams API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: StreamSpec defines the desired state of Stream.
            properties:
              forward:
                description: Stream forward configuration
                properties:
                  hosts:
                    description: List of your forward hosts; if specified, this will
                      take priority over the service.
                    items:
                      properties:
                        hostName:
                          description: The host to forward to (This must be a valid
                            DNS name or IP address)
                          maxLength: 255
                          minLength: 1
                          pattern: ^((([a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)\.)*[a-zA-Z]{2,63}|((25[0-5]|2[0-4][0-9]|[0-1]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[0-1]?[0-9][0-9]?)|(([0-9a-fA-F]{1,4}:){7}([0-9a-fA-F]{1,4}|:))|(::([0-9a-fA-F]{1,4}:){0,6}[0-9a-fA-F]{1,4}))$
                          type: string
                        hostPort:
                          description: Service Target Port is the port to forward
                            to
                          format: int32
                          maximum: 65535
                          minimum: 1
                          type: integer
                      required:
                      - hostName
                      - hostPort
                      type: object
                    type: array
                  service:
                    description: Service resource reference to be forwarded to
                    properties:
                      name:
                        description: |-
                          Name of the service resource to forward to
                          IP and port of the service will be used as the forwarding target
                          Only ClusterIP and LoadBalancer services are supported
                        maxLength: 255
                        minLength: 1
                        type: string
                      namespace:
                        description: Namespace of the service resource to forward
                          to
                        pattern: ^[a-z]([-a-z0-9]*[a-z0-9])?$
                        type: string
                      port:
                        description: Force forwarding to a known service port
                        format: int32
                        maximum: 65535
                        minimum: 1
                        type: integer
                    required:
                    - name
                    type: object
                  tcpForwarding:
                    description: Has TCP Forwarding
                    enum:
                    - true
                    - false
                    type: boolean
                  udpForwarding:
                    description: Has UDP Forwarding
                    enum:
                    - true
                    - false
                    type: boolean
                type: object
              incomingPort:
                description: Incoming Port
                maximum: 65535
                minimum: 1
                type: integer
              overwriteIncomingPortWithForwardPort:
                description: If True the incoming port will be overwritten with the
                  forward port
                enum:
                - true
                - false
                type: boolean
              ssl:
                description: Ssl configuration for the stream
                properties:
                  certificateId:
                    description: |-
                      Bind existing certificate id to the stream
                      CustomCertificate has priority over LetsencryptCertificate and  CustomCertificate
                    type: integer
                  customCertificate:
                    description: |-
                      Custom Certificate name managed by the customCertificate resource
                      CustomCertificate has priority over LetsencryptCertificate
                    properties:
                      name:
                        description: Name of the custom certificate resource
                        type: string
                      namespace:
                        description: Namespace of the custom certificate resource
                        pattern: ^[a-z]([-a-z0-9]*[a-z0-9])?$
                        type: string
                    required:
                    - name
                    type: object
                  letsEncryptCertificate:
                    description: Letsencrypt Certificate name managed by the letsencryptCertificate
                      resource
                    properties:
                      name:
                        description: Name of the letsencrypt certificate resource
                        type: string
                      namespace:
                        description: Namespace of the letsencrypt certificate resource
                        pattern: ^[a-z]([-a-z0-9]*[a-z0-9])?$
                        type: string
                    required:
                    - name
                    type: object
                type: object
              token:
                description: Token resource, if not provided, the operator will try
                  to find a token with `token-nginxpm` name in the same namespace
                  as the proxyhost is created or in the `nginxpm-operator-system`
                  namespace or in the `default` namespace
                properties:
                  name:
                    description: Name of the token resource
                    type: string
                  namespace:
                    description: Namespace of the token resource
                    pattern: ^[a-z]([-a-z0-9]*[a-z0-9])?$
                    type: string
                required:
                - name
                type: object
            required:
            - forward
            - incomingPort
            type: object
          status:
            description: StreamStatus defines the observed state of Stream.
            properties:
              conditions:
                description: Represents the observations of a Stream's current state.
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              forwardingPort:
                description: Forwarding port
                type: integer
              id:
                description: Stream ID from remote Nginx Proxy Manager instance
                type: integer
              incomingPort:
                description: Incoming port
                type: integer
              online:
                description: Online status from remote Nginx Proxy Manager instance
                enum:
                - true
                - false
                type: boolean
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
