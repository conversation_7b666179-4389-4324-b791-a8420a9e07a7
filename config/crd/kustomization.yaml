# This kustomization.yaml is not intended to be run by itself,
# since it depends on service name and namespace that are out of this kustomize package.
# It should be run by config/default
resources:
- bases/nginxpm-operator.io_tokens.yaml
- bases/nginxpm-operator.io_proxyhosts.yaml
- bases/nginxpm-operator.io_letsencryptcertificates.yaml
- bases/nginxpm-operator.io_customcertificates.yaml
- bases/nginxpm-operator.io_accesslists.yaml
- bases/nginxpm-operator.io_streams.yaml
# +kubebuilder:scaffold:crdkustomizeresource

patches:
# [WEBHOOK] To enable webhook, uncomment all the sections with [WEBHOOK] prefix.
# patches here are for enabling the conversion webhook for each CRD
# +kubebuilder:scaffold:crdkustomizewebhookpatch

# [CERTMANAGER] To enable cert-manager, uncomment all the sections with [CERTMANAGER] prefix.
# patches here are for enabling the CA injection for each CRD
#- path: patches/cainjection_in_tokens.yaml
#- path: patches/cainjection_in_proxyhosts.yaml
#- path: patches/cainjection_in_letsencryptcertificates.yaml
#- path: patches/cainjection_in_customcertificates.yaml
# +kubebuilder:scaffold:crdkustomizecainjectionpatch

# [WEBHOOK] To enable webhook, uncomment the following section
# the following config is for teaching kustomize how to do kustomization for CRDs.

#configurations:
#- kustomizeconfig.yaml
