apiVersion: nginxpm-operator.io/v1
kind: LetsEncryptCertificate
metadata:
  labels:
    app.kubernetes.io/name: nginxpm-operator
    app.kubernetes.io/managed-by: kustomize
  name: letsencryptcertificate-sample
spec:
  token:
    name: token-sample
    namespace: nginxpm-operator-system
  domainNames:
    - example.com
    - www.example.com
  letsEncryptEmail: <EMAIL>
  dnsChallenge: # Optional
    provider: acmedns
    providerCredentials:
      secret:
        name: dns-credentials-sample

---
apiVersion: v1
kind: Secret
metadata:
  name: dns-credentials-sample
type: Opaque
data:
  credentials: YWRtaW4=
