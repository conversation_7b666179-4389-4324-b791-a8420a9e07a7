apiVersion: nginxpm-operator.io/v1
kind: ProxyHost
metadata:
  labels:
    app.kubernetes.io/name: nginxpm-operator
  name: proxyhost-sample
spec:
  token:
    name: token-sample
    namespace: default

  domainNames:
    - example.com

  forward:
    scheme: http
    # service:
    #   name: nginx-service
    #   namespace: default
    # Or you can use the hosts configuration
    hosts:
      - hostName: *********** # HostName|IP
        hostPort: 80

    # advancedConfig: ""

  # Access List
  # accessList:
  #   name: admin-access # Access list resource
  #   namespace: default
  #   accessListId: 1 # if you know the accessList id of an existing accessList in the nginx-proxy-manager instance (optional)

  # bindExisting: true
  # blockExploits: true
  # websocketSupport: true
  # cachingEnabled: false
  # Add custom configuration to the proxyhost, at your own risk

  customLocations:
    - locationPath: /
      forward:
        scheme: http
        service:
          name: nginx-service
        # Or you can use the host configuration
        # hosts:
        #   - hostName: *********** # HostName|IP
        #     hostPort: "80"

  # Enable ssl
  ssl:
    autoCertificateRequest: true
    sslForced: true
    http2Support: true

    # letsEncryptEmail: <EMAIL>

    # certificateId: 1 # if you know the certificate id of an existing certificate in the nginx-proxy-manager instance

    # customCertificate:
    #   name: custom-certificate-sample
    #   namespace: nginxpm-operator-system

    # letsEncryptCertificate:
    #   name: letsencrypt-certificate-sample
    #   namespace: nginxpm-operator-system

    # hstsEnabled: false # More info https://en.wikipedia.org/wiki/HTTP_Strict_Transport_Security
    # hstsSubdomains: false
