apiVersion: nginxpm-operator.io/v1
kind: AccessList
metadata:
  labels:
    app.kubernetes.io/name: nginxpm-operator
    app.kubernetes.io/managed-by: kustomize
  name: accesslist-sample
spec:
  token:
    name: token-sample
    namespace: nginxpm-operator-system

  name: internal-access

  satisfyAny: true
  passAuth: false

  authorizations:
    - username: admin
      password: password

  clients:
    - address: ************/24
      directive: allow
